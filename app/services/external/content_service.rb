module External
  class ContentService
    attr_reader :success, :errors, :records

    def initialize(parameters, remote_ip)
      @parameters = parameters
      @remote_ip = remote_ip
      @errors = []
      @records = []
      @skip_mandatory_fields = false
      @skip_business_rules = false
      @skip_input_rules = false
      @skip_validation_url = false
      @skip_webhook = false
    end

    def save
      ActiveRecord::Base.transaction do
        xml_content = Nokogiri::XML(@parameters)

        xml_content.css('business').each do |business_xml|
          business = Business.where(business_group_id: business_xml['group_guid']).find_by(id: business_xml['guid'])

          if business.present?
            business_xml.css('contents/content').each do |content_xml|
              content = create_or_load_content(business, content_xml)

              if content_xml.css('step').empty?
                @records << content
              else
                @skip_mandatory_fields = skip_attributes_xml?(content_xml, 'skip_mandatory_fields')
                @skip_business_rules = skip_attributes_xml?(content_xml, 'skip_business_rules')
                @skip_input_rules = skip_attributes_xml?(content_xml, 'skip_input_rules')
                @skip_validation_url = skip_attributes_xml?(content_xml, 'skip_validation_rules')
                @skip_field_validations = skip_attributes_xml?(content_xml, 'skip_field_validations')
                @skip_webhook = skip_attributes_xml?(content_xml, 'skip_webhook')
                @steps_to_change_ids = content_xml.xpath('.//step').first['steps_to_change_ids']&.split(',')

                next if content.blank?

                answer_errors = []

                content.current_answers.each do |answer|
                  answer_xml = content_xml.css("step[guid='#{answer.step_id}']")[0]
                  answer.reload

                  if answer_xml.present?
                    if answer.available_at.present?
                      update_answer(answer, answer_xml)
                    else
                      answer_errors << I18n.t('content.step_is_not_available_for_filling', step_name: answer.step.name, scope: 'activerecord.errors.services')
                    end
                  else
                    answer_errors << I18n.t('content.tag_not_found_for_step', step_name: answer.step.name, scope: 'activerecord.errors.services')
                  end
                end

                @errors.concat(answer_errors)

                @records << content unless answer_errors.size == content.answers.size
              end
            end
          else
            @errors << I18n.t('content.business_not_fount', guid: business_xml['guid'], scope: 'activerecord.errors.services')
          end
        end

        @success = !@records.empty?

        @errors = [I18n.t('content.invalid_xml', scope: 'activerecord.errors.services')] if xml_content.children.empty?
      end
    rescue StandardError => e
      @success = false
      @errors = [e.message]
    end

    private

    def create_or_load_content(business, content_xml)
      content = business.contents.find_by(id: content_xml['guid'])

      if content.blank?
        created_by_id = content_xml['created_by'] || content_xml['created_by_id']

        if created_by_id.blank?
          step_node = content_xml.xpath('.//step').first
          created_by_id = step_node&.attributes['user']&.value if step_node
        end

        service = ::ContentService.new(
                                        business_id: business.id,
                                        parent_id: content_xml['parent_id'],
                                        created_by_id: created_by_id,
                                        remote_ip: @remote_ip
                                      )
        service.create

        if service.success
          content = service.record
        else
          @errors << I18n.t('content.error_saving_xml_content', content_xml: content_xml.to_xml, errors: service.errors.join(','), scope: 'activerecord.errors.services')
        end
      end

      if content_xml['update_created_by'] == 'true'
        updated_created_by_id = content_xml['created_by_id'] || content_xml['created_by']

        if updated_created_by_id.blank?
          step_node = content_xml.xpath('.//step').first
          updated_created_by_id = step_node&.attributes['user']&.value if step_node
        end

        if updated_created_by_id.present?
          result = content.update_column(:created_by_id, updated_created_by_id)
          content.reload
        end
      end
      content
    end

    def skip_attributes_xml?(content_xml, attribute)
      step_node = content_xml.xpath('.//step').first
      attribute_value = step_node&.attributes[attribute]&.value
      attribute_value == 'true'
    end

    def update_answer(answer, answer_xml)
      xml_values = {}

      fields = answer.step.fields.kept.index_by(&:id)

      answer_xml.css('template/field').group_by { |field_xml| field_xml['guid'] }.each do |guid, fields_xml|
        field = fields[guid]
        next if field.blank?
        is_multiple = %i[multiple upload multiple_reference].include?(field.type.to_sym)

        if is_multiple
          value = fields_xml.map { |field_xml| field_xml['value'].split('|') }.flatten
        else
          value = fields_xml.first['value']

          if value.present? && value.is_a?(String) && field.link?
            begin
              value = JSON.parse(value)
            rescue
              next
            end
          end
        end

        xml_values[guid] = value
      end

      answer.data['values'] = (answer&.data['values'] || {}).merge(xml_values)

      answer_params = {
        'user_id' => answer_xml['user'],
        'content_id' => answer.content_id
      }

      answer_decorated = AnswerDecorator.decorate(
        answer,
        current_user: User.new(id: answer_params['user_id']),
        context: {
          skip_verification_url: ['true', true].include?(answer_xml['skip_external_input']),
          skip_business_rules: ['true', true].include?(answer_xml['skip_business_rules']),
          skip_input_rules: ['true', true].include?(answer_xml['skip_input_rules'])
        }
      ).values || {}

      answer_params = answer_decorated.merge(answer_params)

      service = ::AnswerService.new(
        answer_params.merge(
          'remote_ip' => @remote_ip,
          'skip_verification_url' => ['true', true].include?(
            answer_xml['skip_external_validation']
          ),
          'skip_mandatory_fields' => @skip_mandatory_fields,
          'skip_business_rules' => @skip_business_rules,
          'skip_validation_rules' => @skip_validation_url,
          'skip_field_validations' => @skip_field_validations,
          'skip_webhook' => @skip_webhook,
          'steps_to_change_ids' => @steps_to_change_ids,
          'origin' => Answer.origins[:api]
        )
      )

      service.update(answer.id)

      if service.success
        service.record
      else
        @errors << I18n.t('content.could_not_update_step', step_name: answer.step.name, content_id: answer.content_id, errors: service.errors.join(','), scope: 'activerecord.errors.services')
        nil
      end
    end
  end
end
