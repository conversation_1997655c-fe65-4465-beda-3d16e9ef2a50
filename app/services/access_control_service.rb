class AccessControlService < BaseRulesRunner
  include Query<PERSON><PERSON><PERSON>

  def initialize(business_id, current_user, contents)
    @business = Business.find(business_id)
    @current_user = current_user
    @contents = contents

    @context = :access_control

    @rules = get_rules

    @joined_steps = []
  end

  def run
    return @contents unless @rules.exists?

    actual_permissions = user_permissions_for_business
    @rules.find_each do |rule|
      rule['rule_actions'].each do |action|
        if action['target']['class'] == 'Department'
          actual_permissions = actual_permissions.where.not(department_id: action['target']['id'])
        elsif action['target']['class'] == 'User'
          actual_permissions = actual_permissions.where.not(user_id: action['target']['id'])
        end
      end
    end
    return @contents if actual_permissions.present?

    rules_clauses = []
    current_user_departments_ids = @current_user.departments.pluck(:id).to_a
    @rules.find_each do |rule|
      actions_to_user = rule['rule_actions'].select { |action| action['target']['class'] == 'User' && action['target']['id'] == @current_user.id }
      actions_for_users_departments = rule['rule_actions'].select { |action| action['target']['class'] == 'Department' && current_user_departments_ids.include?(action['target']['id']) }
      actions = actions_to_user.present? ? actions_to_user : actions_for_users_departments

      can_access = actions.any? { |action| action['can_access'] }
      cannot_access = actions.any? { |action| !action['can_access'] }
      next if (can_access && cannot_access) || !(can_access || cannot_access)

      rule_clause = process_rules(rule['rules']['rules'])
      rule_clause = "not (#{rule_clause})" unless can_access
      rules_clauses << rule_clause
    end

    @contents = @contents.where(Arel::Nodes::SqlLiteral.new(rules_clauses.join(' or ')))

    @contents
  end

  private

  def process_rules(rules)
    rules_clauses = []
    rules.each do |rule_clause|
      if rule_clause.key? 'rules'
        rules_clauses << "(#{process_rules(rule_clause['rules'])})"
        next
      end

      step = Step.find(rule_clause['field']&.split(':')&.first)
      field_id = rule_clause['field']&.split(':')&.last
      operator = get_operator(rule_clause['operator'])
      value = rule_clause['value']

      if @joined_steps.exclude?(step.id)
        @contents = @contents.joins(build_answer_join(step))
        @joined_steps << step.id
      end

      rules_clauses << %{a#{step.order}."data"->'values'->>'#{field_id}' #{operator} $$#{value}$$}
    end

    rules_clauses.join(' or ')
  end

  def get_operator(operator)
    case operator
    when 'equals'
      '='
    when 'not_equal'
      '!='
    when 'lower'
      '<'
    when 'greater'
      '>'
    when 'regex_match'
      '~'
    when 'regex_not_match'
      '!~'
    when 'contains'
      'ILIKE'
    when 'not_contains'
      'NOT ILIKE'
    else
      raise ArgumentError, "Operador desconhecido: #{operator}"
    end
  end

  def user_permissions_for_business
    StepPermission
      .for_user(@current_user.id)
      .edit
      .where(step_id: @business.steps.select(:id))
  end

  def get_access_control_rules
    DependentFieldRule
      .access_control
      .where(business_id: @business.id)
      .with_access_for_user_and_departments(@current_user.id, @current_user.departments.pluck(:id))
  end
end
