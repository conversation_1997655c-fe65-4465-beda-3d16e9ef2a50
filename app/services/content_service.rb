class ContentService
  attr_reader :success, :errors, :total_contents, :total_destroyed
  attr_accessor :record

  def initialize(parameters = {}, validator_klass = nil, base_fields = nil)
    @parameters = validator_klass ? validator_klass.new(parameters, base_fields).perform : parameters

    @parents = @parameters.delete(:parents)

    @params = @parameters.delete(:params)
    @current_user = @parameters.delete(:user)
    @updated_by_user = @parameters.delete(:updated_by_user)
    @remote_ip = @parameters.delete(:remote_ip)
    @skip_rules = @parameters.delete(:skip_rules) || {}
    @content_id = @parameters.delete(:content_id)
    return if answer_values.blank?

    @first_answer_values = parameters.slice(*answer_values)
    @parameters = @parameters.except(*answer_values)
  end

  def create
    ActiveRecord::Base.transaction do
      content = if @content_id
                  Content.find(@content_id)
                else
                  Content.new(@parameters)
                end

      content.assign_attributes(draft: true, created_by_ip: @remote_ip)

      if content.business_id.present? && content.answers.empty?
        content.name = content.business.name

        content.business.active_steps.each_with_index do |step, index|
          answer = Answer.new(step:, position: index, status: :pending, data: {}, content_id: content&.id)
          answer.available_at = Time.zone.now if index.zero?
          answer.skip_mandatory_fields = false
          content.answers << answer
        end
      end

      unless content.created_by_id.present?
        content.created_by_id = @current_user&.id
      end

      content.save!

      update_first_answer_of_subcontent(content)
      success_result(content)
    end
  rescue StandardError => e
    @success = false
    @errors = [e.message]
  end

  def update(record_id)
    record = Content.find(record_id)
    record.assign_attributes(@parameters)

    update_first_answer_of_subcontent(record)

    record.save!

    success_result(record)
  rescue StandardError => e
    @success = false
    @errors = [e.message]
  end

  def update_first_answer_of_subcontent(content)
    return unless content.parent_id.present? && @first_answer_values.present?

    sub_answer = content.answers.first

    answer_params = { content_id: content&.id, user_id: @updated_by_user.present? ? @updated_by_user : (content.created_by_id || content.parent.created_by_id), remote_ip: @remote_ip }.merge(@first_answer_values)
    answer_service = AnswerService.new(answer_params.with_indifferent_access.merge('origin' => Answer.origins[:client], 'parents' => @parents))
    answer_service.update(sub_answer&.id)

    raise StandardError, I18n.t('content.subbusiness_saving', errors: answer_service.errors.join(','), scope: 'activerecord.errors.services') unless answer_service.success
  end

  def success_result(content)
    @success = true
    @errors = []
    @record = content
  end

  def destroy(content_id, params = {})
    content = Content.find(content_id)

    content.deletion_reason = params[:deletion_reason]
    content.current_user = @current_user
    content.current_user_ip = params[:current_user_ip]

    if content.discard
      @success = true
    else
      @success = false
      @errors = content.errors.full_messages
    end
  end

  def restore(content_id, params = {})
    content = Content.with_discarded.find(content_id)

    content.current_user = @current_user
    content.current_user_ip = params[:current_user_ip]

    if content.undiscard
      @success = true
    else
      @success = false
      @errors = content.errors.full_messages
    end
  end

  private

  def answer_values
    return @answer_values if @answer_values

    business = Business.includes(:steps).find_by(id: @parameters[:business_id])

    return [] if business.blank? || business.steps.empty?

    allowed_values = Template
                            .joins(:step_templates, :fields)
                            .where(step_templates: { step_id: business.steps.select(:id) })
                            .where(fields: { deleted_at: nil })
                            .distinct
                            .pluck(:'fields.id')
                            .map(&:to_sym)

    @answer_values = allowed_values.flatten
  end
end
