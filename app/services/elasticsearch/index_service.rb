# frozen_string_literal: true

require_relative 'base_service'

module Elasticsearch
  class IndexService < BaseService
    def create_index(business_id)
      validate_business_id!(business_id)

      fields = Field.for_business(business_id).for_elasticsearch.select('fields.*, steps.order step_order')

      index_body = build_index_body(fields)

      handle_elasticsearch_error do
        if client.indices.exists?(index: index_name(business_id))
          log_operation('update_mapping', { business_id: business_id })
          client.indices.put_mapping(index: index_name(business_id), body: index_body)
        else
          log_operation('create_index', { business_id: business_id })
          client.indices.create(index: index_name(business_id), body: index_body)
        end
      end
    end

    def delete_index(business_id)
      validate_business_id!(business_id)

      handle_elasticsearch_error(ignore_not_found: true) do
        log_operation('delete_index', { business_id: business_id })
        client.indices.delete(index: index_name(business_id), ignore_unavailable: true)
      end
    end

    def get_mapping(business_id)
      validate_business_id!(business_id)

      handle_elasticsearch_error do
        client.perform_request('GET', "#{index_name(business_id)}/_mapping").body[index_name(business_id)]
      end
    end

    def get_field_mapping(business_id, field_names)
      validate_business_id!(business_id)
      raise ServiceError, 'Field names are required' if field_names.blank?

      encoded_field_names = field_names.map { |name| ERB::Util.url_encode(name) }.join(',')

      handle_elasticsearch_error do
        client.perform_request('GET', "#{index_name(business_id)}/_mapping/field/#{encoded_field_names}").body
      end
    end

    private

    def build_index_body(fields)
      {
        settings: build_index_settings,
        mappings: build_field_mappings(fields)
      }
    end

    def build_index_settings
      {
        index: {
          analysis: build_analysis_settings,
          "mapping.total_fields.limit": MAPPING_TOTAL_FIELDS_LIMIT
        }
      }
    end

    def build_analysis_settings
      {
        analyzer: build_analyzers,
        char_filter: build_char_filters,
        normalizer: build_normalizers
      }
    end

    def build_analyzers
      {
        text_analyzer: {
          type: 'custom',
          tokenizer: 'standard',
          filter: %w[lowercase asciifolding]
        }
      }
    end

    def build_char_filters
      {
        uppercase_filter: { type: 'pattern_replace', pattern: '([A-Z])', replacement: 'U' },
        lowercase_filter: { type: 'pattern_replace', pattern: '([a-z])', replacement: 'L' },
        number_filter: { type: 'pattern_replace', pattern: '([0-9])', replacement: 'N' },
        whitespace_filter: { type: 'pattern_replace', pattern: '([\s])', replacement: 'S' },
        accent_filter: {
          type: 'mapping',
          mappings: build_accent_mappings
        }
      }
    end

    def build_accent_mappings
      %w[
        ç=>c Ç=>C á=>a é=>e í=>i ó=>o ú=>u ý=>y Á=>A É=>E Í=>I Ó=>O Ú=>U Ý=>Y
        à=>a è=>e ì=>i ò=>o ù=>u À=>A È=>E Ì=>I Ò=>O Ù=>U ã=>a õ=>o ñ=>n
        ä=>a ë=>e ï=>i ö=>o ü=>u ÿ=>y Ä=>A Ë=>E Ï=>I Ö=>O Ü=>U Ã=>A Õ=>O Ñ=>N
        â=>a ê=>e î=>i ô=>o û=>u Â=>A Ê=>E Î=>I Ô=>O Û=>U
      ]
    end

    def build_normalizers
      {
        keyword_normalizer: { type: 'custom', filter: %w[lowercase asciifolding] },
        pattern_normalizer: {
          type: 'custom',
          char_filter: %w[accent_filter uppercase_filter lowercase_filter number_filter whitespace_filter]
        }
      }
    end

    def build_field_mappings(fields)
      properties = {}

      fields.find_each do |field|
        elasticsearch_name = determine_field_name(properties, field)
        next if elasticsearch_name.blank?

        if NESTED_FIELD_TYPES.include?(field.elasticsearch_type)
          sub_business_properties = build_sub_business_properties(field.reference_sub_business)
          properties["#{elasticsearch_name} - Objeto"] = { type: 'object', properties: sub_business_properties }
          properties["#{elasticsearch_name} - Aninhado"] = { type: 'nested', properties: sub_business_properties }
        else
          properties[elasticsearch_name] = build_field_property(field)
        end
      end

      { properties: properties }
    end

    def determine_field_name(properties, field)
      base_name = field.elasticsearch_name
      return nil if base_name.blank?

      properties.key?(base_name) ? "#{base_name}_#{field.step_order + 1}" : base_name
    end

    def build_field_property(field, is_sub_business_field: false)
      base_property = { type: field.elasticsearch_type }

      # Adicionar campos específicos baseados no tipo
      case field.elasticsearch_type
      when 'text'
        base_property[:analyzer] = 'text_analyzer'
        add_keyword_fields(base_property) unless is_sub_business_field
      when 'long', 'float'
        # Campos numéricos não precisam de subcampos keyword/pattern
        base_property
      when 'date'
        # Campos de data podem ter formato específico
        base_property[:format] = 'strict_date_optional_time||epoch_millis'
      when 'nested'
        # Campos nested não precisam de subcampos
        base_property
      else
        # Fallback para outros tipos
        add_keyword_fields(base_property) unless is_sub_business_field
      end

      base_property
    end

    def add_keyword_fields(property)
      property[:fields] = {
        keyword: { type: 'keyword' },
        pattern: { type: 'keyword', normalizer: 'pattern_normalizer' }
      }
    end

    def build_sub_business_properties(sub_business)
      properties = {
        'content_id' => { type: 'keyword' }
      }

      Field.for_business(sub_business.id).for_elasticsearch.find_each do |sub_field|
        properties[sub_field.elasticsearch_name] = build_field_property(sub_field, is_sub_business_field: true)
      end

      properties
    end
  end
end
