# frozen_string_literal: true

require_relative 'base_service'

module Elasticsearch
  class ClientService < BaseService
    def count_documents(business_id, query_body = {})
      validate_business_id!(business_id)

      handle_elasticsearch_error do
        log_operation('count', { business_id: business_id, query: query_body })
        client.count(index: index_name(business_id), body: query_body)
      end
    end

    def index_exists?(business_id)
      validate_business_id!(business_id)

      handle_elasticsearch_error do
        client.indices.exists?(index: index_name(business_id))
      end
    end

    def perform_request(method, path)
      handle_elasticsearch_error do
        client.perform_request(method, path)
      end
    end

    def search(index_pattern, body)
      handle_elasticsearch_error do
        log_operation('search', { index: index_pattern, body_size: body.to_s.length })
        client.search(index: index_pattern, body: body)
      end
    end

    def bulk_index(documents)
      return if documents[:body].empty?

      handle_elasticsearch_error do
        log_operation('bulk', { index: documents[:index], document_count: documents[:body].size })
        client.bulk(documents)
      end
    end

    def delete_document(business_id, document_id)
      validate_business_id!(business_id)

      handle_elasticsearch_error(ignore_not_found: true) do
        log_operation('delete_document', { business_id: business_id, document_id: document_id })
        client.delete(index: index_name(business_id), id: document_id)
      end
    end

    def delete_by_query(index_pattern, query_body)
      handle_elasticsearch_error(ignore_not_found: true) do
        log_operation('delete_by_query', { index: index_pattern, query: query_body })
        client.delete_by_query(index: index_pattern, body: query_body)
      end
    end

    def index_document(business_id, document_id, document_body)
      validate_business_id!(business_id)

      handle_elasticsearch_error do
        log_operation('index_document', { business_id: business_id, document_id: document_id })
        client.index(
          index: index_name(business_id),
          id: document_id,
          body: document_body
        )
      end
    end
  end
end
