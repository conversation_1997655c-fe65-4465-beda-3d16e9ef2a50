# frozen_string_literal: true

require_relative '../base_service'
require_relative 'field_transformer'

module Elasticsearch
  module Transformers
    class ContentTransformer < BaseService
      def initialize
        @field_transformer = FieldTransformer.new
      end

      def transform(content)
        validate_content!(content)

        document = build_base_document(content)
        add_parent_id(document, content)
        add_field_data(document, content)

        document
      end

      private

      attr_reader :field_transformer

      def build_base_document(content)
        {
          CONTENT_ID => content.id,
          BUSINESS_ID => content.business_id,
          NOTE => content.note,
          CONTENT_STATUS => content.translated_status,
          BUSINESS_NAME => business_name(content),
          DELETION_REASON => content.deletion_reason.to_s,
          UPDATED_BY_NAME => content.last_update_by.try(:name).to_s,
          DELETED_AT => content.deleted_at.try(:iso8601),
          CREATED_AT => content.try(:created_at)&.iso8601,
          UPDATED_AT => content.try(:updated_at)&.iso8601
        }
      end

      def business_name(content)
        content.business.try(:name) || 'NEGÓCIO INEXISTENTE'
      end

      def add_parent_id(document, content)
        document[PARENT_ID] = content.parent_id if content.parent_id.present?
      end

      def add_field_data(document, content)
        fields = Field.for_business(content.business_id).for_elasticsearch.select('fields.*, steps.order step_order')

        Answer.where(content_id: content.id).order(:position).find_each do |answer|
          add_answer_metadata(document, answer)
          add_answer_field_values(document, answer, fields)
        end
      end

      def add_answer_metadata(document, answer)
        position = answer.position

        document["answers.#{position}.updated_at"] = answer.updated_at.iso8601
        document["answers.#{position}.authorized_at"] = answer.authorized_at.try(:iso8601)
        document["answers.#{position}.available_at"] = answer.available_at.try(:iso8601)
        document["answers.#{position}.concluded_at"] = answer.concluded_at.try(:iso8601)
        document["answers.#{position}.filled_at"] = answer.filled_at.try(:iso8601)
        document["answers.#{position}.first_fill_at"] = answer.first_fill_at.try(:iso8601)
        document["answers.#{position}.user_name"] = answer.user.try(:name)
        document["answers.#{position}.authorizer_name"] = answer.authorizer.try(:name)
        document["answers.#{position}.created_by_name"] = answer.created_by.try(:name)
        document["answers.#{position}.status"] = answer.translated_status
      end

      def add_answer_field_values(document, answer, fields)
        fields.where(steps: { id: answer.step_id }).find_each do |field|
          # Use answer.values directly instead of answer.data.dig('values', field.id)
          # This ensures consistent access to field values regardless of how they're stored
          value = field_transformer.transform_value(answer.content, field, answer.values&.[](field.id))
          elasticsearch_name = determine_field_name(document, field)

          next if elasticsearch_name.blank?

          if field.type == SUB_BUSINESS_FIELD_TYPE
            document["#{elasticsearch_name} - Objeto"] = value
            document["#{elasticsearch_name} - Aninhado"] = value
          else
            document[elasticsearch_name] = value
          end
        end
      end

      def determine_field_name(document, field)
        base_name = field.elasticsearch_name
        return nil if base_name.blank?

        document.key?(base_name) ? "#{base_name}_#{field.step_order + 1}" : base_name
      end
    end
  end
end
