# frozen_string_literal: true

require_relative '../base_service'

module Elasticsearch
  module Transformers
    class FieldTransformer < BaseService
      def transform_value(content, field, value)
        return nil if value.blank? && field.type != SUB_BUSINESS_FIELD_TYPE

        case field.type
        when 'integer'
          transform_integer_value(value, field)
        when 'decimal'
          transform_decimal_value(value, field)
        when DATE_FIELD_TYPE
          transform_date_value(value, field)
        when LINK_FIELD_TYPE
          transform_link_value(value)
        when SUB_BUSINESS_FIELD_TYPE
          transform_sub_business_value(content, field)
        else
          transform_text_value(value, field)
        end
      end

      private

      def transform_integer_value(value, field)
        return nil unless field.expected_value_field?(value)

        return nil unless value.to_s.match?(INTEGER_REGEX)
        value.to_i
      end

      def transform_decimal_value(value, field)
        return nil if field.expected_value_field?(value)

        return nil unless value.to_s.match?(DECIMAL_REGEX)
        value.to_f
      end

      def transform_date_value(value, field)
        return nil if field.expected_value_field?(value)

        return nil unless value.to_s.match?(DATE_REGEX)

        begin
          # Garantir que a data seja enviada no formato ISO
          Date.parse(value).iso8601
        rescue Date::Error
          nil
        end
      end

      def transform_link_value(value)
        if value.is_a?(Hash)
          value['url']
        elsif value.is_a?(String)
          JSON.parse(value)['url']
        else
          nil
        end
      rescue
        nil
      end

      def transform_text_value(value, field)
        return nil if field.expected_value_field?(value)

        unless value.is_a?(String)
          return value.to_s if value.is_a?(Numeric)
          return value.to_s if value.respond_to?(:to_s)
          return nil
        end

        value.truncate(MAX_TEXT_SIZE, omission: '')
      end

      def hash_or_array?(value)
        value.is_a?(Hash) || value.is_a?(Array)
      end

      def transform_sub_business_value(content, field)
        return nil unless field.reference_sub_business

        field.reference_sub_business.contents
             .where(parent_id: content.id, draft: false)
             .pluck(:id)
             .map { |sub_content_id| build_sub_content_data(sub_content_id) }
      end

      def build_sub_content_data(sub_content_id)
        content_data = { content_id: sub_content_id }

        sub_content_values = ContentValue.where(content_id: sub_content_id).joins(:field)
        values = extract_answer_values(sub_content_id)

        add_field_values_to_content_data(content_data, sub_content_values, values)

        content_data
      end

      def extract_answer_values(sub_content_id)
        Answer.where(content_id: sub_content_id)
              .select(:data)
              .map(&:values)
              .reject(&:nil?)
              .reduce({}, :merge)
      end

      def add_field_values_to_content_data(content_data, sub_content_values, values)
        field_ids = sub_content_values.pluck('fields.id')

        Field.where(id: field_ids)
             .where.not(type: UPLOAD_FIELD_TYPE)
             .select(:id, :label, :type)
             .find_each do |sub_field|

          sub_field_value = values[sub_field.id].presence
          next unless sub_field_value

          elasticsearch_name = sub_field.elasticsearch_name
          next if elasticsearch_name.blank?

          processed_value = transform_value(content_data, sub_field, sub_field_value)

          content_data[elasticsearch_name] = processed_value
        end
      end
    end
  end
end
