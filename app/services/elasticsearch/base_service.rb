# frozen_string_literal: true

require_relative '../../../lib/elasticsearch/constants'
require_relative '../../../lib/elasticsearch/configuration'

module Elasticsearch
  class BaseService
    include Constants

    class ServiceError < StandardError; end

    protected

    def client
      Configuration.client
    end

    def validate_business_id!(business_id)
      raise ServiceError, 'Business ID is required' if business_id.blank?
    end

    def validate_content!(content)
      raise ServiceError, 'Content is required' if content.blank?
    end

    def handle_elasticsearch_error(ignore_not_found: false)
      yield
    rescue ::Elastic::Transport::Transport::Errors::NotFound => e
      if ignore_not_found
        Rails.logger.info "Elasticsearch resource not found (ignored for delete operation) [#{e.class.name}]: #{e.message}"
        Rails.logger.info "Backtrace: #{e.backtrace.first(3).join("\n")}" if Rails.env.development?
        return nil
      end

      Rails.logger.warn "Elasticsearch index not found [#{e.class.name}]: #{e.message}"
      Rails.logger.warn "Backtrace: #{e.backtrace.first(3).join("\n")}" if Rails.env.development?

      error_message = build_error_message('elastic_searcher.index_not_found', e)
      raise ServiceError, error_message
    rescue ::Elastic::Transport::Transport::Errors::BadRequest => e
      Rails.logger.error "Elasticsearch bad request [#{e.class.name}]: #{e.message}"
      Rails.logger.error "Backtrace: #{e.backtrace.first(3).join("\n")}" if Rails.env.development?

      error_message = build_error_message('elastic_searcher.invalid_request', e)
      raise ServiceError, error_message
    rescue StandardError => e
      Rails.logger.error "Elasticsearch error [#{e.class.name}]: #{e.message}"
      Rails.logger.error "Backtrace: #{e.backtrace.first(3).join("\n")}" if Rails.env.development?

      if e.message.include?('An HTTP line is larger than 4096 bytes')
        error_message = build_error_message('elastic_searcher.too_many_fields', e)
        raise ServiceError, error_message
      end

      # For unknown errors, preserve the original message with context
      raise ServiceError, "#{I18n.t('elastic_searcher.generic_error', scope: 'activerecord.errors.services')}: #{e.message}"
    end

    def log_operation(operation, details = {})
      Rails.logger.info "Elasticsearch #{operation}: #{details.inspect}" if Rails.env.development?
    end

    private

    def build_error_message(translation_key, exception)
      translated_message = I18n.t(translation_key, scope: 'activerecord.errors.services')

      if Rails.env.development? || Rails.env.test?
        # In development/test, include technical details for debugging
        "#{translated_message} (#{exception.class.name}: #{exception.message})"
      else
        # In production, include original message for worker logs but keep it clean
        "#{translated_message}: #{exception.message}"
      end
    end
  end
end
