# frozen_string_literal: true

require_relative '../../lib/elasticsearch/constants'
require_relative '../../lib/elasticsearch/configuration'
require_relative 'elasticsearch/index_service'
require_relative 'elasticsearch/document_service'
require_relative 'elasticsearch/search_service'
require_relative 'elasticsearch/client_service'

class ElasticSearcherService
  include Elasticsearch::Constants

  attr_reader :search_body, :parameters

  class ServiceError < StandardError; end

  def initialize(parameters = {})
    @parameters = parameters.dup
    @parameters[:page_size] = (@parameters.delete('page_size') || PAGE_SIZE).to_i
    @parameters[:kind] = @parameters.delete('kind') || 'simplified'

    @index_service = Elasticsearch::IndexService.new
    @document_service = Elasticsearch::DocumentService.new
    @search_service = Elasticsearch::SearchService.new(@parameters)
    @client_service = Elasticsearch::ClientService.new
  end

  # Delegate to client service for backward compatibility
  def client
    Elasticsearch::Configuration.client
  end

  # Index management methods - delegate to IndexService
  def create_index(business_id)
    @index_service.create_index(business_id)
  end

  def delete_index(business_id)
    @index_service.delete_index(business_id)
  end

  def count_index(business_id, body = {})
    @client_service.count_documents(business_id, body)
  end

  # Document management methods - delegate to DocumentService
  def put_document(content)
    @document_service.index_document(content)
  end

  def put_documents(content_ids)
    @document_service.index_documents(content_ids)
  end

  def delete_document(content_id)
    @document_service.delete_document(content_id)
  end

  # Search methods - delegate to SearchService
  def search
    result = @search_service.search
    @search_body = @search_service.search_body
    result
  end

  def data_profile
    result = @search_service.data_profile
    @search_body = @search_service.search_body
    result
  end

  def field_stats
    result = @search_service.field_stats
    @search_body = @search_service.search_body
    result
  end

  def find_similar_terms
    result = @search_service.find_similar_terms
    @search_body = @search_service.search_body
    result
  end

  def mapping_information
    @search_service.mapping_information
  end

  def mapping_information_by_index(business_id)
    @index_service.get_mapping(business_id)
  end

  # Backward compatibility methods
  def search_source
    @search_service.send(:search_source_fields)
  end

  # Backward compatibility - these methods are now handled by the new services
  # but kept for any existing code that might call them directly

  private

  # Legacy method - now handled by DocumentService
  def content_to_document(content)
    Elasticsearch::Transformers::ContentTransformer.new.transform(content)
  end
end
