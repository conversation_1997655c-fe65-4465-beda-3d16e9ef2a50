# == Schema Information
#
# Table name: dependent_reference_fields
#
#  id              :uuid             not null, primary key
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  field_id        :uuid
#  parent_field_id :uuid
#  parent_step_id  :uuid
#  step_id         :uuid
#
# Indexes
#
#  index_dependent_reference_fields_on_field_id         (field_id)
#  index_dependent_reference_fields_on_parent_field_id  (parent_field_id)
#  index_dependent_reference_fields_on_parent_step_id   (parent_step_id)
#  index_dependent_reference_fields_on_step_id          (step_id)
#
# Foreign Keys
#
#  fk_rails_...  (field_id => fields.id)
#  fk_rails_...  (parent_field_id => fields.id)
#  fk_rails_...  (parent_step_id => steps.id)
#  fk_rails_...  (step_id => steps.id)
#
class DependentReferenceFieldsController < ApplicationController
  before_action :authenticate_administrator!, except: %i[index]
  before_action :authenticate_member!, only: %i[index]

  def index
    respond_to do |format|
      format.json do
        searcher = DependentReferenceFieldSearcher.new(search_params)

        @records = searcher.search
      end
    end
  end

  def show
    @record = DependentReferenceField.find(params[:id])
  end

  def create
    service = DependentReferenceFieldService.new(record_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = DependentReferenceFieldService.new(record_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = DependentReferenceFieldService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def search_params
    params.permit(:field_id, :step_id)
  end

  def record_params
    params.permit(:step_id, :field_id, :parent_field_id, :parent_step_id)
  end
end
