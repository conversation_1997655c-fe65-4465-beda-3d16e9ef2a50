class DataProfileController < ApplicationController
  def index
    service = ElasticSearcherService.new(params)

    data_profile = service.data_profile
    mappings = service.mapping_information

    presenter = DataProfileSummaryPresenter.new(data_profile, mappings)

    render json: presenter.as_json, status: :ok
  rescue ElasticSearcherService::ServiceError => e
    render json: { errors: [e.message] }, status: :bad_request
  end

  def field_stats
    service = ElasticSearcherService.new(params)

    data_profile = service.field_stats
    mappings = service.mapping_information

    presenter = DataProfileFieldStatsPresenter.new(data_profile, mappings)

    render json: presenter.as_json, status: :ok
  rescue ElasticSearcherService::ServiceError => e
    render json: { errors: [e.message] }, status: :bad_request
  end

  def similarity
    service = ElasticSearcherService.new(params)

    data_profile = service.find_similar_terms
    mappings = service.mapping_information

    presenter = DataProfileSimilarityPresenter.new(data_profile, mappings)

    render json: presenter.as_json, status: :ok
  rescue ElasticSearcherService::ServiceError => e
    render json: { errors: [e.message] }, status: :bad_request
  end
end
