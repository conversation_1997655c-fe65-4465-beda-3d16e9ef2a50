module External
  class UsersController < BaseController
    before_action :validate_api_key
    before_action :set_paper_trail_whodunnit

    def create
      respond_to do |format|
        format.json do
          service = UserService.new(user_params)

          [true, 'true'].include?(params[:create_confirmed]) ? service.create : service.create_unconfirmed(login_url)

          if service.success
            render json: { id: service.record.id }, status: :created
          else
            render json: { errors: service.errors }, status: :unprocessable_entity
          end
        end

        format.xml do
          render xml: { error: 'xml format is not supported' }, status: :not_acceptable
        end
      end
    end

    def update
      respond_to do |format|
        format.json do
          service = UserService.new(user_params)

          service.update(params[:id])

          if service.success
            head :ok
          else
            render json: { errors: service.errors }, status: :unprocessable_entity
          end
        end

        format.xml do
          render xml: { error: 'xml format is not supported' }, status: :not_acceptable
        end
      end
    end

    def destroy
      respond_to do |format|
        format.json do
          service = UserService.new

          service.destroy(params[:id])

          if service.success
            head :no_content
          else
            render json: { errors: service.errors }, status: :bad_request
          end
        end

        format.xml do
          render xml: { error: 'xml format is not supported' }, status: :not_acceptable
        end
      end
    end

    private

    def login_url
      "#{ActionController::Base.asset_host.sub('://', "://#{Apartment::Tenant.current}.")}/login"
    end

    def user_params
      params.permit(:name, :email, :password, :password_confirmation, :limited, :coordinator, :notification, :approved, :chat_enabled, block_menus: [], department_ids: [])
    end
  end
end
