module External
  module V2
    class ContentsController < External::BaseController
      include Query<PERSON>elper

      before_action :authenticate_with_token!
      before_action :set_content, only: :update
      before_action :set_business, only: %i[upsert]

      def index
        offset = params[:offset] || 0
        limit = params[:limit] || 10

        if limit.to_i > 500
          render json: { errors: I18n.t("api.v2.contents.errors.max_limit") }, status: :unprocessable_entity
          return
        end

        steps = Step.where(business_id: content_params[:business_id]).select(:id, :order)
        @contents = Content
                          .includes(steps: { templates: %i[ fields ] })
                          .joins(steps.map { |step| build_answer_join(step) })
                          .where(business_id: content_params[:business_id])
                          .order(created_at: :desc)

        unless content_params[:query].nil?
          return render json: { errors: ['Incorrect parameters for the request.'] }, status: :unprocessable_entity if validate_query(content_params[:query])

          query = build_conditions(content_params[:query].to_h, steps)

          @contents = @contents.where(query)
        end

        @total_contents = @contents.count
        @total_next_contents = @contents.offset(offset.to_i + limit.to_i).count
        @has_more_contents = @total_next_contents.positive?

        @contents = @contents.offset(offset).limit(limit)

      rescue ArgumentError => e
        render json: { errors: [e.message] }, status: :unprocessable_entity
      end

      def show
        @content = Content.find_by(id: params[:id])

        render json: { errors: ['Content not found.'] }, status: :not_found if @content.nil?
      end

      def create
        return render json: { errors: ['Incorrect parameters for the request.'] }, status: :unprocessable_entity if params['steps'].nil?

        service = new_content_service(params)
        service.save

        if service.success
          result = {}
          result[:id] = service.record.id
          result[:sub_ids] = service.sub_content_ids
          render json: result, status: :created
        else
          render json: { errors: service.errors }, status: :unprocessable_entity
        end
      end

      def update
        return render json: { errors: ['Incorrect parameters for the request.'] }, status: :unprocessable_entity if params['steps'].nil?

        service = new_content_service(params.merge(content_id: @content.id))
        service.save

        if service.success
          result = {}
          result[:id] = service.record.id
          result[:sub_ids] = service.sub_content_ids
          render json: result, status: :ok
        else
          render json: { errors: service.errors }, status: :unprocessable_entity
        end
      end

      def upsert
        return render json: { errors: ['Incorrect parameters for the request.'] }, status: :unprocessable_entity if params['key_fields'].nil?

        business = Business.find(params[:business_id])
        if business.sub_business? && !params[:parent_id].present?
          return render json: { errors: ['Parent content id is required for sub business.'] }, status: :unprocessable_entity
        elsif business.sub_business? && params[:parent_id].present?
          parent_content = Content.where(id: params[:parent_id])
          if parent_content.empty?
            return render json: { errors: ['Não foi encontrado nenhum parent_id com esse GUID.'] }, status: :not_found
          end
        end


        key_fields = {}
        params['key_fields'].map do |field_key_id, value|
          key_fields[field_key_id[:id]] = field_key_id[:value]
        end

        parent_id = Content.for_pks(@business.parent&.id, key_fields).first.try(:id) if @business.sub_business?
        content = Content.for_pks(@business.id, key_fields)
        content = content.where(parent_id: parent_id) if parent_id.present?

        service = External::V2::ContentService.new(params.merge(
                                                     created_by_id: current_user.try(:id),
                                                     remote_ip: remote_ip(request),
                                                     current_user: current_user
                                                   ).merge(content.empty? ? {content_id: nil} : {content_id: content&.first&.id}))

        service.save

        if service.success
          render json: { id: service.record.id }, status: :ok
        else
          render json: { errors: service.errors }, status: :unprocessable_entity
        end
      end

      private

      def new_content_service(service_params)
        service = External::V2::ContentService.new(service_params.merge(
                                           created_by_id: current_user.try(:id),
                                           remote_ip: remote_ip(request),
                                           current_user: current_user
                                         ))
        service
      end

      def validate_query(query)
        query["conditions"].any? do |condition|
          condition.key?("field") && condition.key?("field_metadata")
        end
      end

      def steps_from_query(query)
        query[:conditions].each_with_object([]) do |condition, steps|
          steps << condition[:step] if condition.key?(:step)
          steps.concat(steps_from_query(condition)) if condition.key?(:conditions)
        end.flatten.uniq
      end

      def build_conditions(query, steps = [])
        return build_condition(query, steps) if query.is_a?(Hash) && (query['field'] || query['field_metadata'])

        if query.is_a?(Hash) && query['operator'] == 'OR'
          query['conditions'].map { |condition| build_conditions(condition, steps) }.reduce(:or)
        elsif query.is_a?(Hash) && query['operator'] == 'AND'
          query['conditions'].map { |condition| build_conditions(condition, steps) }.reduce(:and)
        else
          raise ArgumentError, 'Invalid query'
        end
      end

      def build_condition(condition, steps)
        field_metadata, field, operator, value, step = condition.values_at('field_metadata', 'field', 'operator', 'value', 'step')

        if field_metadata.present?
          table = if step
            Answer.arel_table.alias("a#{steps.find(step).order}")
          else
            Content.arel_table
          end

          case operator
          when '!=', '<>'
            return table[field_metadata].not_eq(value)
          when '='
            return table[field_metadata].eq(value)
          when '<'
            return table[field_metadata].lt(value)
          when '>'
            return table[field_metadata].gt(value)
          when '=>'
            return table[field_metadata].gteq(value)
          when '<='
            return table[field_metadata].lteq(value)
          when 'LIKE'
            return table[field_metadata].matches("%#{value}%")
          when 'NOT LIKE'
            return table[field_metadata].does_not_match("%#{value}%")
          when 'IN'
            return table[field_metadata].in(value.split(',').map(&:strip))
          when 'BETWEEN'
            values = value.split(',').map(&:strip)

            raise ArgumentError, 'Invalid value for BETWEEN operator' if values.size != 2

            range = Range.new(*values)

            return table[field_metadata].between(range)
          else
            raise ArgumentError, 'Invalid operator'
          end
        end

        if field.present?
          order = steps.find(step)&.order if step
          answer_alias = order ? "a#{order}" : 'answers'
          case operator
          when '='
            Arel::Nodes::InfixOperation.new(
              '->>',
              Arel::Nodes::SqlLiteral.new("#{answer_alias}.data->'values'"),
              Arel::Nodes::SqlLiteral.new("'#{field}'")
            ).eq(value)
          when '!='
            Arel::Nodes::InfixOperation.new(
              '->>',
              Arel::Nodes::SqlLiteral.new("#{answer_alias}.data->'values'"),
              Arel::Nodes::SqlLiteral.new("'#{field}'")
            ).not_eq(value)
          when 'LIKE'
            Arel::Nodes::InfixOperation.new(
              '->>',
              Arel::Nodes::SqlLiteral.new("#{answer_alias}.data->'values'"),
              Arel::Nodes::SqlLiteral.new("'#{field}'")
            ).matches("%#{value}%")
          when 'NOT LIKE'
            Arel::Nodes::InfixOperation.new(
              '->>',
              Arel::Nodes::SqlLiteral.new("#{answer_alias}.data->'values'"),
              Arel::Nodes::SqlLiteral.new("'#{field}'")
            ).does_not_match("%#{value}%")
          when 'IN'
            Arel::Nodes::InfixOperation.new(
              '->>',
              Arel::Nodes::SqlLiteral.new("#{answer_alias}.data->'values'"),
              Arel::Nodes::SqlLiteral.new("'#{field}'")
            ).in(value.split(',').map(&:strip))
          when 'BETWEEN'
            values = value.split(',').map(&:strip)

            raise ArgumentError, 'Invalid value for BETWEEN operator' if values.size != 2

            range = Range.new(*values)

            Arel::Nodes::InfixOperation.new(
              '->>',
              Arel::Nodes::SqlLiteral.new("#{answer_alias}.data->'values'"),
              Arel::Nodes::SqlLiteral.new("'#{field}'")
            ).between(range)
          else
            raise ArgumentError, 'Invalid operator'
          end
        end
      end

      def content_params
        params.permit(:business_id, query: {})
      end

      def set_content
        @content = Content.find(params[:id])
      end

      def set_business
        @business = Business.find(params[:business_id])
      end
    end
  end
end
