class External::V2::UsersController < External::BaseController
  before_action :authorize_policy, only: %i[create update destroy show index]
  before_action :set_user, only: %i[destroy show]

  def index
    if params[:limit].to_i > 500
      render json: { errors: I18n.t("api.v2.users.errors.max_limit") }, status: :unprocessable_entity
      return
    end

    offset = params[:offset] || 0
    limit = params[:limit] || 10

    @total_users = User.count
    @total_next_users = User.offset(offset.to_i + limit.to_i).count
    @has_more_users = @total_next_users.positive?

    @users = User.offset(offset).limit(limit)
  end

  def show
    render json: { errors: I18n.t("api.v2.users.errors.user_not_found") }, status: :not_found if @user.nil?
  end

  def create
    service = UserService.new(user_params)
    [true, 'true'].include?(params[:create_confirmed]) ? service.create : service.create_unconfirmed(login_url)

    if service.success
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    entity_to_find = %i[email id].find { |key| params[key].present? }
    user = User.find_by(entity_to_find => params[entity_to_find])

    return render json: { errors: I18n.t("api.v2.users.errors.user_not_found") }, status: :not_found unless user

    if user.update(user_params)
      render json: { id: user.id }, status: :ok
    else
      render json: { errors: user.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    if @user.destroy
      render json: { id: @user.id }, status: :ok
    else
      render json: { errors: @user.errors }, status: :bad_request
    end
  end

  private

  def authorize_policy
    allowed_actions = %w[index? show? create? update? destroy?]
    method_name = "#{params[:action]}?"

    unless allowed_actions.include?(method_name)
      render json: { error: I18n.t("api.v2.users.errors.unauthorized") }, status: :unauthorized
      return
    end

    policy = External::V2::UserPolicy.new(administrator_credentials)
    unless policy.public_send(method_name)
      render json: { error: I18n.t("api.v2.users.errors.unauthorized") }, status: :unauthorized
    end
  end

  def login_url
    "#{ActionController::Base.asset_host.sub('://', "://#{Apartment::Tenant.current}.")}/login"
  end

  def user_params
    params.permit(:name, :email, :password, :password_confirmation, :limited, :coordinator, :notification, :approved, :confirmed_at, :chat_enabled, block_menus: [], department_ids: [])
  end

  def set_user
    @user = User.find_by(id: params[:id])
  end
end
