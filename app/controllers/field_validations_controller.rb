# == Schema Information
#
# Table name: field_validations
#
#  id            :uuid             not null, primary key
#  data          :string
#  error_message :string           default(""), not null
#  operator      :integer          not null
#  type          :integer          not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  field_id      :uuid
#
# Indexes
#
#  index_field_validations_on_field_id  (field_id)
#
# Foreign Keys
#
#  fk_rails_...  (field_id => fields.id)
#
class FieldValidationsController < ApplicationController
  before_action :authenticate_administrator!

  def index
    @records = FieldValidation.where(field_id: params[:field_id])
  end

  def create
    service = FieldValidationService.new(field_validation_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = FieldValidationService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def field_validation_params
    params.permit(:field_id, field_validations: %i[type operator data error_message])
  end
end
