# == Schema Information
#
# Table name: widgets
#
#  id                :uuid             not null, primary key
#  category_agg      :string
#  chart_type        :integer          default("pie"), not null
#  embedded_code     :string
#  order             :integer          default(0), not null
#  size              :integer          default("small"), not null
#  value_agg         :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  category_field_id :uuid
#  search_id         :uuid             not null
#  statistic_id      :uuid             not null
#  value_field_id    :uuid
#
# Indexes
#
#  index_widgets_on_category_field_id  (category_field_id)
#  index_widgets_on_search_id          (search_id)
#  index_widgets_on_statistic_id       (statistic_id)
#  index_widgets_on_value_field_id     (value_field_id)
#
# Foreign Keys
#
#  fk_rails_...  (category_field_id => fields.id)
#  fk_rails_...  (search_id => searches.id)
#  fk_rails_...  (statistic_id => statistics.id)
#  fk_rails_...  (value_field_id => fields.id)
#
class WidgetsController < ApplicationController
  before_action :authenticate_administrator!
  before_action :authenticate_member!
  around_action :check_access_tenant

  def show
    @widget = Widget.find(params[:id])
  end

  def create
    service = WidgetService.new(widget_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = WidgetService.new(widget_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = WidgetService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def widget_params
    params.permit(:chart_type, :embedded_code, :size, :order, :search_id, :statistic_id, :category_field_id, :value_field_id, :category_agg, :value_agg)
  end
end
