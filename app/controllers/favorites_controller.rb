# == Schema Information
#
# Table name: favorites
#
#  id          :uuid             not null, primary key
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  business_id :uuid
#  user_id     :uuid
#
# Indexes
#
#  index_favorites_on_business_id  (business_id)
#  index_favorites_on_user_id      (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (business_id => businesses.id)
#  fk_rails_...  (user_id => users.id)
#
class FavoritesController < ApplicationController
  before_action :authenticate_user!

  def index
    @records = Favorite.where(user_id: current_user.id)
    @records = @records.where(business_id: params[:business_id]) if params[:business_id].present?
  end

  def as_menu
    menu = Favorite.where(user_id: current_user.id).map do |favorite|
      { id: favorite.business_id, name: favorite.business.name, business_group_name: favorite.business.business_group.name }
    end
    menu = menu.group_by { |d| d[:business_group_name] }.sort.map do |business_group_name, menus|
      { name: business_group_name, children: menus }
    end

    render json: menu, status: :ok
  end

  def create
    service = FavoriteService.new(create_params)

    service.create

    if service.success?
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = FavoriteService.new

    service.destroy(params[:id])

    if service.success?
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def create_params
    params.permit(:business_id).merge(user_id: current_user.id)
  end
end
