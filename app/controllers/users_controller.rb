# == Schema Information
#
# Table name: users
#
#  id                     :uuid             not null, primary key
#  allow_password_change  :boolean          default(FALSE), not null
#  approved               :boolean          default(TRUE), not null
#  authorization_token    :string
#  block_menus            :string           default([]), is an Array
#  chat_enabled           :boolean          default(FALSE)
#  confirmation_sent_at   :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  coordinator            :boolean          default(FALSE)
#  current_departments    :string           default([]), not null, is an Array
#  current_sign_in_at     :datetime
#  current_sign_in_ip     :string
#  ddd                    :string(4)        default("")
#  ddi                    :string(4)        default("")
#  deleted_at             :datetime
#  email                  :string
#  encrypted_password     :string           default(""), not null
#  failed_attempts        :integer          default(0), not null
#  image                  :string
#  last_active_at         :datetime
#  last_sign_in_at        :datetime
#  last_sign_in_ip        :string
#  limited                :boolean          default(FALSE)
#  locked_at              :datetime
#  name                   :string
#  nickname               :string
#  notification           :boolean          default(TRUE), not null
#  password_changed_at    :datetime
#  phone                  :string(11)       default("")
#  provider               :string           default("email"), not null
#  remember_created_at    :datetime
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  sign_in_count          :integer          default(0), not null
#  tokens                 :jsonb
#  uid                    :string           default(""), not null
#  unconfirmed_email      :string
#  unlock_token           :string
#  welcome_video_watched  :boolean          default(FALSE)
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_users_on_deleted_at            (deleted_at)
#  index_users_on_email                 (email) UNIQUE
#  index_users_on_password_changed_at   (password_changed_at)
#  index_users_on_reset_password_token  (reset_password_token) UNIQUE
#  index_users_on_uid_and_provider      (uid,provider) UNIQUE
#  index_users_on_unlock_token          (unlock_token) UNIQUE
#
class UsersController < ApplicationController
  before_action :set_paper_trail_whodunnit
  before_action :authenticate_administrator!, except: %i[index notifications top_answers welcome_video_watched token_to_confirm_step]
  before_action :authenticate_member!, only: %i[index notifications top_answers]

  def index
    respond_to do |format|
      format.datatable do
        return if current_administrator.blank?

        render json: UserDatatable.new(params), status: :ok
      end
      format.json do
        @users =
          if current_administrator.present?
            just_kept = [true, 'true'].include?(params[:only_active])

            scope = User.all
            scope = scope.where(deleted_at: nil) if just_kept

            scope.order('deleted_at NULLS FIRST, name ASC')
          elsif current_user.present?
            User.all.select(:id, :name).order('deleted_at NULLS FIRST').order(:name)
          end
      end
    end
  end

  def show
    @user = User.find(params[:id])
  end

  def create
    service = UserService.new(user_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = UserService.new(user_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = UserService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def activate
    service = UserService.new

    service.restore(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def notifications
    @records = []

    if current_user.present? && current_user.notification?
      @records = Content.not_sub_content.not_draft.where.not(status: :done).joins(:business).where(businesses: { notification: true }).joins(:answers).where(answers: { user_id: current_user.id }).order(updated_at: :desc).distinct.kept.limit(20)
      @records = @records.where.not(contents: { id: JSON.parse(params[:removed_content_ids]) }) if params[:removed_content_ids].present?
    end

    render :notifications
  end

  def top_answers
    statuses = request.url.split("?").last.split("&").reject { |param| !param.include?("status") }.map { |status| status.split("status=").last }

    @records = []

    if current_user.present?
      @records = Answer.joins(:step, content: :business).where(businesses: { show_on_top_answers: true }).includes(content: { business: :key_fields })

      @records = @records.where(created_by_id: current_user.id) if current_user.limited?
      @records = @records.where(contents: { business_id: StepPermission.for_user(current_user).joins(step: :business).distinct('businesses.id').select('businesses.id') })
      @records = @records.where(contents: { status: statuses }) if statuses.present?

      @records = @records.order(updated_at: :desc).limit(20)
    end

    render :top_answers
  end

  def welcome_video_watched
    service = UserService.new(welcome_video_watched_params)

    service.update(user_params[:id])

    head :ok
  end

  def lock
    service = UserService.new
    service.lock(user_params[:id])
    head :ok
  end

  def unlock
    service = UserService.new
    service.unlock(user_params[:id])
    head :ok
  end

  def token_to_confirm_step
    service = UserService.new(token_to_confirm_step_params)
    service.token_to_confirm_step

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def id_params
    params.permit(:id)
  end

  def user_params
    params.permit(:id, :name, :ddi, :ddd, :phone, :email, :password, :password_confirmation, :limited, :coordinator, :notification, :approved, :chat_enabled, block_menus: [], department_ids: [])
  end

  def welcome_video_watched_params
    params.permit(:welcome_video_watched)
  end

  def token_to_confirm_step_params
    params.permit(:step_id, :email, :answer_id, :administrator)
  end
end
