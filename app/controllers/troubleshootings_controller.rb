# == Schema Information
#
# Table name: public.troubleshootings
#
#  id                  :uuid             not null, primary key
#  data                :jsonb
#  duration_in_seconds :float
#  end_at              :datetime
#  external            :boolean
#  response_code       :integer
#  start_at            :datetime
#  subdomain           :string
#  url                 :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  request_id          :string
#  thread_id           :string
#
class TroubleshootingsController < ApplicationController
  before_action :authenticate_member!

  def index
    respond_to do |format|
      format.datatable do
        @troubleshooting_datatable = TroubleshootingDatatable.new(params, current_user: current_user)
        authorize @troubleshooting_datatable
        render json: @troubleshooting_datatable, status: :ok
      end
      format.json do
        @troubleshootings = []
      end
    end
  end
end
