# == Schema Information
#
# Table name: fields
#
#  id                             :uuid             not null, primary key
#  allow_add_new                  :boolean          default(FALSE)
#  allow_to_select_deleted_option :boolean          default(FALSE)
#  char_max_limit                 :integer
#  default_value                  :jsonb
#  deleted_at                     :datetime
#  enable_char_count              :boolean          default(FALSE), not null
#  enabled                        :boolean          default(TRUE)
#  height                         :integer          default("small")
#  input_variable                 :string
#  label                          :string           not null
#  options                        :json
#  order                          :integer          not null
#  output_variable                :string
#  reference_value_use_key_fields :boolean          default(FALSE)
#  required                       :boolean          default(FALSE)
#  show_on_form                   :boolean          default(FALSE)
#  size                           :integer          not null
#  text_transformation            :integer
#  tooltip                        :string
#  type                           :integer          not null
#  update_disabled                :boolean          default(FALSE)
#  validate_sub_business_contents :boolean
#  visible                        :boolean          default(TRUE)
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  reference_business_id          :uuid
#  reference_field_id             :uuid
#  reference_sub_business_id      :uuid
#  reference_value_field_id       :uuid
#  template_id                    :uuid
#
# Indexes
#
#  index_fields_on_deleted_at                 (deleted_at)
#  index_fields_on_reference_business_id      (reference_business_id)
#  index_fields_on_reference_field_id         (reference_field_id)
#  index_fields_on_reference_sub_business_id  (reference_sub_business_id)
#  index_fields_on_reference_value_field_id   (reference_value_field_id)
#  index_fields_on_template_id                (template_id)
#
# Foreign Keys
#
#  fk_rails_...  (reference_business_id => businesses.id)
#  fk_rails_...  (reference_field_id => fields.id)
#  fk_rails_...  (reference_sub_business_id => businesses.id)
#  fk_rails_...  (reference_value_field_id => fields.id)
#  fk_rails_...  (template_id => templates.id)
#
class FieldsController < ApplicationController
  include SkipTranslation

  before_action :set_paper_trail_whodunnit
  before_action :authenticate_administrator!, except: %i[available_options pk_fields_for_business index show add_option]
  before_action :authenticate_user!, only: %i[add_option]
  before_action :load_template, only: %i[show]
  before_action :set_company_enable_internationalization, only: %i[index show]

  RESULTS_LIMIT = 50

  def index
    @records = FieldSearcher.new(search_params).results
  end

  def pk_fields_for_business
    @records = FieldSearcher.new(search_params).pk_fields_for_business

    render 'index'
  end

  def show
    @field = @template.present? ? @template.fields.find(params[:id]) : Field.find(params[:id])
  end

  def create
    service = FieldService.new(field_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  rescue FieldsErrors::FieldDefaultValueMatchTypeError => e
    render json: { errors: e.message }, status: :unprocessable_entity
  end

  def update
    service = FieldService.new(field_update_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  rescue FieldsErrors::FieldDefaultValueMatchTypeError => e
    render json: { errors: e.message }, status: :unprocessable_entity
  end

  def available_options
    render json: FieldSearcher.new(params.merge(limit: RESULTS_LIMIT)).available_options_for_reference_field
  end

  def destroy
    service = FieldService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def activate
    service = FieldService.new

    service.activate(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def add_option
    service = FieldService.new(add_option_params)

    service.add_option(params[:id])

    if service.success
      @field = service.record
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def load_template
    @template = Template.find(params[:template_id]) if params[:template_id]
  end

  def field_params
    params.permit(:validate_sub_business_contents, :type, :size, :label, :tooltip, :required, :visible, :enabled, :template_id, :show_on_form,
                  :reference_business_id, :reference_field_id, :reference_value_field_id, :reference_sub_business_id,
                  :input_variable, :output_variable, :allow_add_new, :update_disabled, :text_transformation,
                  :allow_to_select_deleted_option, :reference_value_use_key_fields, :height, :enable_char_count, :char_max_limit,
                  options: %i[label value order]).merge(default_value_params)
  end

  def default_value_params
    permitted = FieldValidators::DefaultValueParamsValidator.new(params[:default_value]).perform(params[:type])
    params.permit(permitted)
  end

  def field_update_params
    field_params.merge(params.permit(:order))
  end

  def search_params
    params.permit(:step_id, :business_id, :q, :limit, business_ids: [])
  end

  def add_option_params
    params.permit(:label, :value)
  end

  def default_value_params
    permitted = FieldValidators::DefaultValueParamsValidator.new(params[:default_value]).perform(params[:type])
    params.permit(permitted)
  end
end
