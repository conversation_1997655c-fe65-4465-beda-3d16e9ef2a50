# == Schema Information
#
# Table name: dependent_field_rules
#
#  id                 :uuid             not null, primary key
#  condition_operator :integer
#  condition_value    :string
#  deleted_at         :datetime
#  description        :string(50)       default("")
#  error_message      :string
#  field_disabled     :boolean          default(FALSE)
#  field_hidden       :boolean          default(FALSE)
#  field_operator     :integer
#  field_required     :boolean
#  field_value        :string
#  field_visible      :boolean          default(FALSE)
#  restrictions       :jsonb
#  rule_actions       :jsonb
#  rule_type          :integer          default("dependent"), not null
#  rules              :jsonb
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  business_id        :uuid
#  field_id           :uuid
#  parent_field_id    :uuid
#  parent_step_id     :uuid
#  step_id            :uuid
#
# Indexes
#
#  index_dependent_field_rules_on_business_id      (business_id)
#  index_dependent_field_rules_on_field_id         (field_id)
#  index_dependent_field_rules_on_parent_field_id  (parent_field_id)
#  index_dependent_field_rules_on_parent_step_id   (parent_step_id)
#  index_dependent_field_rules_on_restrictions     (restrictions) USING gin
#  index_dependent_field_rules_on_step_id          (step_id)
#
# Foreign Keys
#
#  fk_rails_...  (business_id => businesses.id)
#  fk_rails_...  (field_id => fields.id)
#  fk_rails_...  (parent_field_id => fields.id)
#  fk_rails_...  (parent_step_id => steps.id)
#  fk_rails_...  (step_id => steps.id)
#
class DependentFieldRulesController < ApplicationController
  before_action :authenticate_administrator!

  def index
    respond_to do |format|
      format.json do
        if search_params[:business_id].present?
          @records = DependentFieldRule.where(business_id: search_params[:business_id])
        else
          searcher = DependentFieldRuleSearcher.new(search_params)
          @records = searcher.search
        end
      end
    end
  end

  def show
    @record = DependentFieldRule.find(params[:id])
  end

  def create
    params_valid = record_params

    unless validate_params(params_valid)
      render json: { errors: [I18n.t('dependent_field_rules.fill_all')] }, status: :unprocessable_entity
      return
    end

    valid = OperatorDependentFieldRulesValidator.new(params_valid).valid?

    unless valid
      render json: { errors: [I18n.t('dependent_field_rules.fill_all')] }, status: :unprocessable_entity
      return
    end

    service = DependentFieldRuleService.new(params_valid.to_h)

    service.create

    if service.success?
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    params_valid = record_params

    unless validate_params(params_valid)
      render json: { errors: [I18n.t('dependent_field_rules.fill_all')] }, status: :unprocessable_entity
      return
    end

    valid = OperatorDependentFieldRulesValidator.new(params_valid).valid?

    unless valid
      render json: { errors: [I18n.t('dependent_field_rules.fill_all')] }, status: :unprocessable_entity
      return
    end

    service = DependentFieldRuleService.new(params_valid.to_h)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = DependentFieldRuleService.new

    service.destroy(params[:id])

    if service.success?
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def validate_params(params)
    rule_actions = params[:rule_actions]

    return false unless rule_actions

    rule_actions.all? do |rule_action|
      rule_action.key?(:field) || (rule_action.key?(:can_access) && rule_action.key?(:target)) || rule_action.key?(:restrictions)
    end
  end

  def record_params
    params.permit(
      :id,
      :field_required,
      :rule_type,
      :field_disabled,
      :field_hidden,
      :field_visible,
      :business_id,
      :description,
      rules: {},
      restrictions: [:class, :id],
      rule_actions: [
        :can_access,
        :index,
        :error_message,
        :operator,
        :field,
        :field_hidden,
        :field_visible,
        :field_required, :field_disabled,
        :value,
        { target: %i[class id] },
        { restrictions: %i[class id] },
        { value: %i[label value order] },
        { value: [] }
      ]
    )
  end

  def search_params
    params.permit(:business_id)
  end
end
