# == Schema Information
#
# Table name: show_on_list_fields
#
#  id             :uuid             not null, primary key
#  order          :integer          not null
#  order_contents :integer          default("asc"), not null
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  business_id    :uuid
#  field_id       :uuid
#  step_id        :uuid
#
# Indexes
#
#  index_show_on_list_fields_on_business_id  (business_id)
#  index_show_on_list_fields_on_field_id     (field_id)
#  index_show_on_list_fields_on_step_id      (step_id) WHERE (step_id IS NOT NULL)
#
# Foreign Keys
#
#  fk_rails_...  (business_id => businesses.id)
#  fk_rails_...  (field_id => fields.id)
#  fk_rails_...  (step_id => steps.id)
#
class ShowOnListFieldsController < ApplicationController
  include SkipTranslation
  include ShowOnListFieldHelper

  before_action :authenticate_administrator!, except: %i[index]
  before_action :authenticate_member!, only: %i[index]
  before_action :set_company_enable_internationalization, only: %i[index]

  def index
    @records = fields(params[:business_id], params[:include_parent_keys])
  end

  def create
    service = ShowOnListFieldService.new(create_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = ShowOnListFieldService.new(update_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = ShowOnListFieldService.new

    ShowOnListField.where(field_id: params[:id]).destroy_all

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def bulk_create
    service = ShowOnListFieldService.new(bulk_create_params)
    service.bulk_create

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def create_params
    params.permit(:business_id, :field_id, :order, :step_id)
  end

  def update_params
    params.permit(:order, :order_contents)
  end

  def bulk_create_params
    params.permit(:business_id, data: %i[business_id field_id order_contents step_id order])
  end

end
