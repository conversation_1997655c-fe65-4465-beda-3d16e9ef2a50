# == Schema Information
#
# Table name: templates
#
#  id          :uuid             not null, primary key
#  deleted_at  :datetime
#  description :text
#  name        :string           not null
#  variable    :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
# Indexes
#
#  index_templates_on_deleted_at  (deleted_at)
#  index_templates_on_name        (name)
#
class TemplatesController < ApplicationController
  include SkipTranslation

  before_action :set_paper_trail_whodunnit
  before_action :authenticate_administrator!, except: %i[index]
  before_action :authenticate_member!, only: %i[index]
  before_action :set_company_enable_internationalization, only: %i[index show]

  def index
    respond_to do |format|
      format.datatable do
        render json: TemplateDatatable.new(params), status: :ok
      end
      format.json do
        searcher = TemplateSearcher.new(search_params)
        @templates = searcher.search
      end
    end
  end

  def show
    @template = Template.find(params[:id])
  end

  def create
    service = TemplateService.new(template_params)

    service.create

    if service.success
      render json: { id: service.record.id }, status: :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = TemplateService.new(template_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def destroy
    service = TemplateService.new

    service.destroy(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  def activate
    service = TemplateService.new

    service.restore(params[:id])

    if service.success
      head :no_content
    else
      render json: { errors: service.errors }, status: :bad_request
    end
  end

  private

  def search_params
    params.permit(:deleted)
  end

  def template_params
    params.permit(:name, :description, :variable)
  end
end
