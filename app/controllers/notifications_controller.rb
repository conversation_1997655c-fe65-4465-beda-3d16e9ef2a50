# == Schema Information
#
# Table name: notifications
#
#  id                  :uuid             not null, primary key
#  destiny_departments :uuid             default([]), is an Array
#  destiny_users       :uuid             default([]), is an Array
#  discarded_at        :datetime
#  dismissed_by        :uuid             default([]), is an Array
#  message             :text             not null
#  title               :string           not null
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  user_id             :uuid             not null
#
# Indexes
#
#  index_notifications_on_discarded_at  (discarded_at)
#  index_notifications_on_user_id       (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class NotificationsController < ApplicationController
  before_action :authenticate_user!

  def index
    records = Notification.order('created_at DESC').kept.not_dismissed(current_user.id)

    @results = if current_user.departments.blank?
                 (records.without_destination.or(records.for_user(current_user.id))).first(20)
               else
                 (records.without_destination.or(records.for_user(current_user.id)).or(records.for_department(current_user.departments.ids))).first(20)
               end
  end

  def read
    service = NotificationService.new(user_id: current_user.id)
    service.read(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end
end
