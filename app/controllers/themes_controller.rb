# == Schema Information
#
# Table name: public.themes
#
#  id               :bigint           not null, primary key
#  background_color :string           not null
#  button_color     :string           not null
#  menu_color       :string           not null
#  menu_focus_color :string           not null
#  name             :string           not null
#  text_color       :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#
class ThemesController < ApplicationController

  def index
    @records = Theme.all
  end

  def style
    current_company = Company.current

    @theme = current_company.try(:theme) || Theme.first

    render template: 'themes/style', formats: [:css]
  end
end
