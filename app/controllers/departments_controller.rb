# == Schema Information
#
# Table name: departments
#
#  id         :uuid             not null, primary key
#  deleted_at :datetime
#  limited    :boolean          default(FALSE)
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class DepartmentsController < ApplicationController
  before_action :set_paper_trail_whodunnit
  before_action :authenticate_administrator!, except: %i[index]
  before_action :authenticate_member!, only: %i[index]
  around_action :check_access_tenant, only: %i[index]

  def index
    respond_to do |format|
      format.datatable do
        return if current_administrator.blank?

        render json: DepartmentDatatable.new(params), status: :ok
      end
      format.json do
        @records = Department.all.order(:name).distinct

        @records = @records.joins(:users) if params[:with_users]
      end
    end
  end

  def show
    @department = Department.find(params[:id])
  end

  def create
    service = DepartmentService.new(department_params)

    service.create

    if service.success
      head :created
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  def update
    service = DepartmentService.new(department_params)

    service.update(params[:id])

    if service.success
      head :ok
    else
      render json: { errors: service.errors }, status: :unprocessable_entity
    end
  end

  private

  def department_params
    params.permit(:name, :limited)
  end
end
