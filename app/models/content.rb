# == Schema Information
#
# Table name: contents
#
#  id                :uuid             not null, primary key
#  concluded_at      :datetime
#  created_by_ip     :inet
#  deleted_at        :datetime
#  deletion_reason   :string
#  draft             :boolean          default(FALSE)
#  keywords          :string
#  name              :string           not null
#  note              :text
#  status            :integer          default("pending"), not null
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  business_id       :uuid
#  created_by_id     :uuid
#  current_answer_id :uuid
#  deleted_by_id     :uuid
#  parent_id         :uuid
#
# Indexes
#
#  index_contents_on_business_id        (business_id)
#  index_contents_on_created_by_id      (created_by_id)
#  index_contents_on_current_answer_id  (current_answer_id)
#  index_contents_on_deleted_at         (deleted_at)
#  index_contents_on_deleted_by_id      (deleted_by_id)
#  index_contents_on_draft              (draft)
#  index_contents_on_parent_id          (parent_id)
#  index_contents_on_status             (status)
#
# Foreign Keys
#
#  fk_rails_...  (business_id => businesses.id)
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (current_answer_id => answers.id)
#  fk_rails_...  (deleted_by_id => users.id)
#  fk_rails_...  (parent_id => contents.id)
#
class Content < ApplicationRecord
  include SoftDeletable
  include TranslateEnum

  attr_accessor :current_user, :current_user_ip, :visible

  default_scope -> { kept }

  belongs_to :business, optional: false
  belongs_to :created_by, optional: true, class_name: 'User', inverse_of: false
  belongs_to :parent, optional: true, class_name: 'Content', inverse_of: false
  belongs_to :current_answer, class_name: 'Answer', optional: true, inverse_of: false

  has_many :answers, -> { order(position: :asc) }, inverse_of: :content, dependent: :destroy
  has_many :steps, through: :answers
  has_many :content_values, dependent: :destroy

  validates :name, presence: { allow_blank: false }

  validates :concluded_at, presence: true, if: :done?
  validates :concluded_at, absence: true, unless: :done?
  validates :parent_id, presence: true, if: :sub_content?

  validate :valid_parent?, if: :sub_content?
  validate :active_business?, if: proc { business_id.present? }
  validate :valid_pk_id

  before_discard :is_parent_field_required?, if: :sub_content?

  scope :not_draft, -> { where(draft: false) }
  scope :draft, -> { where(draft: true) }
  scope :active, -> { kept.not_draft.joins(:business).where(businesses: { deleted_at: nil }) }
  scope :not_sub_content, -> { joins(:business).where(businesses: { sub_business: false }) }
  scope :for_pks, ->(business_id, values) do
    return Content.where('1 != 1') if Business.find(business_id).key_field_ids.blank? # returns an empty scope if there is no pk_field_ids

    query_hash = Business.find(business_id).key_field_ids.map do |key_field|
      "answers.data->'values'->>'#{key_field}' = #{ActiveRecord::Base.connection.quote(values[key_field].to_s)}"
    end

    step_ids = Step.where(business_id:).where(order: 0).ids

    Content.joins(:answers).where(business_id:).where(query_hash.join(' AND ')).where(answers: { deleted_at: nil, step_id: step_ids })
  end
  scope :children_of, ->(parent_id) { where(parent_id:) }

  attribute :status, :integer
  enum status: {
    pending: 0,
    waiting_authorization: 1,
    done: 2,
    changing: 3,
    under_review: 4,
    rejected: 5
  }
  translate_enum :status

  after_commit  :index_in_elasticsearch, on: %i[create update destroy]

  after_discard do
    answers.update_all(deleted_at: Time.zone.now)

    save_versions
  end

  after_undiscard do
    update_columns(deletion_reason: nil)

    answers.update_all(deleted_at: nil)

    update_columns(deletion_reason: nil)

    save_versions
  end

  def save_versions
    answers.all.find_each do |answer|
      answer.tap do |element|
        element.current_user = @current_user
        element.current_user_ip = @current_user_ip
      end.send(:save_version)
    end
  end

  def sub_content?
    business.try(:sub_business?)
  end

  def values
    answers.map { |a| a.values || [] }
  end

  def current_step
    answers.index(current_answer)
  end

  def current_step_name
    current_answer.try(:step).try(:name)
  end

  def current_answers
    reorder_answers

    answers.done.each(&:update_next_answer_availability)

    answers.order(:position)
  end

  def reorder_answers
    answers_step_ids = answers.pluck(:step_id)
    active_step_ids = business.active_steps.pluck(:id)

    return if answers_step_ids == active_step_ids

    update!(current_answer_id: nil)
    (answers_step_ids - active_step_ids).each do |step_to_remove|
      answers.where(step_id: step_to_remove).find_each do |answer|
        answers.delete(answer)
      end
    end

    checked_step_ids = []
    answers_to_update = []
    answers.each do |answer|
      active_step_index = active_step_ids.index(answer.step_id)

      available_at = if answer.available_at.nil?
                       active_step_index.zero? ? Time.zone.now : nil
                     else
                       answer.available_at
                     end

      if checked_step_ids.include?(answer.step_id)
        answers.delete(answer)
        next
      end

      if answer.position != active_step_index
        answer.position = active_step_index
        answer.skip_reopen_done_answers = true
        answer.available_at = available_at
        answers_to_update << answer
      end

      checked_step_ids << answer.step_id
    end

    answers_to_create = []
    steps_to_create = (active_step_ids - answers_step_ids)
    steps_to_create.each do |step_id|
      active_step_index = active_step_ids.index(step_id)

      available_at = active_step_index.zero? ? Time.zone.now : nil
      answers_to_create << Answer.new(
        step_id:,
        position: active_step_index,
        status: :pending,
        content_id: id,
        data: {},
        skip_reopen_done_answers: true,
        available_at:
      )
    end

    answers_to_update.concat(answers_to_create).each { |answer| answer.save(validate: false) }

    update_current_answer
    update_progress
  end

  def update_current_answer
    reload

    return unless answers.present?

    answer_not_done = first_status_not_done

    answer_id = answer_not_done.present? ? answer_not_done[:answer_id] : answers.last.id
    update_column(:current_answer_id, answer_id)
  end

  def last_update_at
    answers.reorder(updated_at: :desc).pick(:updated_at)
  end

  def last_update_by
    answers.reorder(updated_at: :desc).limit(1).first.try(:user)
  end

  def finished_status?
    done? || rejected?
  end

  def update_progress
    return unless answers.any?(&:concluded?)

    last_answers_status = first_status_not_done

    if answers.all?(&:done?) || last_answers_status.blank?
      concluded_date = concluded_at.presence || answers.reorder(concluded_at: :desc).pick(:concluded_at)
      concluded_date = Time.zone.now if concluded_date.blank?

      update_status(:done, concluded_date)
      return
    end

    case last_answers_status[:status]
    when 'rejected'
      update_status(:rejected)
    when 'changing'
      update_status(:changing)
    when 'under_review'
      update_status(:under_review)
    when 'waiting_authorization'
      update_status(:waiting_authorization)
    else
      update_status(:pending)
    end
  end

  def first_status_not_done
    answer = answers.where.not(status: :done)
                    .select(:id, :status)
                    .limit(1)
                    .first

    return {} if answer.nil?

    {
      answer_id: answer.id,
      status: answer.status
    }
  end

  def show_on_list_values
    all_values('EXISTS (select 1 from show_on_list_fields slf where slf.field_id = fields.id AND slf.business_id = steps.business_id)')
  end

  def show_on_form_values(include_steps: false, user_id:)
    form_values_to_show(include_steps: include_steps, user_id:)
  end

  def all_values(_field_scope)
    company_enable_internationalization = Companies::EnableInternationalizationQuery.call
    answer_values = answers.map do |answer|
      { values: answer.values, step_id: answer.step_id }
    end.compact

    all_values = []
    field_order = -1

    steps.each do |step|
      data = answer_values.find { |answer| answer[:step_id] == step.id }

      next if data.nil? || data[:values].nil?

      step.fields.kept.each do |field|
        order = StepTemplate.where(step_id: step.id, template_id: field.template_id).last.order
        field_order += 1

        next unless field.show_on_form_values? || field.show_on_list_values?(business_id)

        value = data[:values][field.id]

        if field.reference?
          field_option = FieldOption.find_by(field_id: field.id, value:)
          value = field_option.try(:label)
        elsif field.multiple_reference?
          value = FieldOption.where(field_id: field.id, value:).distinct.pluck(:label)
        elsif field.dropdown?
          label_value = field.available_options.detect { |option| option['value'] == value }
          value = label_value['label'] if label_value.present?
        elsif field.text?
          value = value.to_s
        end

        all_values << { id: field.id, label: field.translated_attribute('label', false, company_enable_internationalization), field_type: field.type, value:, order:, field_order: }
      end
    end

    all_values.sort_by { |entry| [entry[:order], entry[:field_order]] }
  end

  def form_values_to_show(include_steps: false, user_id:)
    company_enable_internationalization = Companies::EnableInternationalizationQuery.call

    if include_steps
      step_ids = ::Contents::GetStepsForShowOnFormValuesQuery.call(id, user_id)

      return [] if step_ids.blank?

      resolve_form_values_step_data(step_ids, company_enable_internationalization)
    else
      contents_data_to_show = ::Contents::ShowOnFormValuesQuery.call(id, user_id)

      return [] if contents_data_to_show.blank?

      resolve_form_values_data(contents_data_to_show, company_enable_internationalization)
    end
  end

  def exists_other_content_for_same_pk(new_values = {})
    other_content_for_same_pk(new_values).exists?
  end

  def other_content_for_same_pk(new_values = {})
    values = answers.map(&:values).reject(&:nil?).reduce({}, :merge).merge(new_values)

    scope = Content.for_pks(business_id, values).where.not(id:).where(draft: false)

    scope = scope.where(parent_id:) if parent_id.present?

    scope
  end

  def self.update_keywords(scope = nil)
    sql = "UPDATE CONTENTS SET KEYWORDS =
      (
      SELECT CASE WHEN current_answers.status=0 THEN 'pendente'
        WHEN current_answers.status=1 THEN 'concluído'
        WHEN current_answers.status=2 THEN 'aguardando aprovação'
        WHEN current_answers.status=4 THEN 'em alteração'
        ELSE 'reprovada'
      END || ';'
      || lower(current_answer_step.name) || ';' ||  string_agg(concat(lower(content_value_search.value),','), '; ') AS VALUE
      FROM content_values content_value_search
      INNER JOIN answers current_answers ON contents.current_answer_id = current_answers.id
      INNER JOIN steps current_answer_step ON current_answers.step_id = current_answer_step.id
      INNER JOIN show_on_list_fields ON (show_on_list_fields.field_id = content_value_search.field_id AND show_on_list_fields.business_id = current_answer_step.business_id)
      WHERE content_value_search.content_id = contents.id
      GROUP BY content_value_search.content_id, current_answer_step.name, current_answers.status
    )"

    sql += " WHERE #{scope}" if scope.present?

    ActiveRecord::Base.connection.update(sql)
  end

  def edit_url
    content_url = "#{ActionController::Base.asset_host.sub('://', "://#{Apartment::Tenant.current}.")}/businesses/#{business_id}/contents/#{id}"
    "<a href=\"#{content_url}\" target=\"_blank\">Gostaria de verificar o registro?</a>"
  end

  def undiscard
    return super if valid?

    false
  end

  def visible? = !!visible

  private

  def resolve_form_values_step_data(step_ids, company_enable_internationalization)
    Step.where(id: step_ids).includes(fields: [template: :step_templates]).order(:order).map do |step|
      data = { step: step.name, fields: [] }

      step
        .fields
        .unscope(:order)
        .where({ deleted_at: nil, show_on_form: true })
        .order('step_templates.order ASC, fields.order ASC')
        .each do |field|
        value = answers.find_by(step_id: step.id)&.values&.dig(field.id)

        value = validate_type(field, value)

        data[:fields] << {
          id: field.id,
          label: field.translated_attribute('label', false, company_enable_internationalization),
          field_type: field.type, value:
        }
      end

      data
    end
  end

  def resolve_form_values_data(contents_data_to_show, company_enable_internationalization)
    all_values = []

    contents_data_to_show.each do |content_data|
      field = Field.find(content_data.field_id)

      next unless field.show_on_form_values? || field.show_on_list_values?(business_id)

      field_values_hash = JSON.parse(content_data.field_value || '{}')
      value = field_values_hash[content_data.field_id.to_s] || ''

      value = validate_type(field, value)

      all_values << {
        id: field.id,
        label: field.translated_attribute('label', false, company_enable_internationalization),
        field_type: field.type,
        value:,
        field_order: content_data.field_order,
        step_order: content_data.step_order,
        step_template_order: content_data.step_template_order
      }
    end

    all_values
  end

  def validate_type(field, value)
    case field.type.to_sym
    when :reference
      field_option = FieldOption.find_by(field_id: field.id, value:)
      field_option.try(:label)
    when :multiple_reference
      FieldOption.where(field_id: field.id, value:).distinct.pluck(:label)
    when :dropdown
      label_value = field.available_options.detect { |option| option['value'] == value }
      label_value['label'] if label_value.present?
    when :text
      value.to_s
    else
      value
    end
  end

  def update_status(status, concluded_at = nil)
    update(
      draft: false,
      status:,
      concluded_at:
    )
  end

  def is_parent_field_required?
    return unless Field.kept.find_by(reference_sub_business_id: business_id)&.required?
    return if Content.where(business_id:, draft: false, status: :done, parent_id:).where.not(id:).exists?

    errors.add(:base, I18n.t('content.cant_delete_sub_content', scope: 'activerecord.errors.models'))
    throw :abort
  end

  def active_business?
    errors.add(:business, :inactive) unless Business.kept.exists?(id: business_id)
  end

  def valid_pk_id
    return unless business&.key_fields

    errors.add(:base, I18n.t('content.duplicate_key_field_value', pk_labels: business.key_fields.pluck(:label).join('|'), scope: 'activerecord.errors.models')) if !new_record? && exists_other_content_for_same_pk
  end

  def valid_parent?
    return if parent.blank?

    sub_business_fields_for_parent = Field.for_business(parent.business_id).where(type: :sub_business, reference_sub_business_id: business_id)

    errors.add(:base, I18n.t('content.invalid_business', business_name: business.name, scope: 'activerecord.errors.models')) unless sub_business_fields_for_parent.exists?
  end

  def index_in_elasticsearch
    return unless should_index_in_elasticsearch?

    Elasticsearch::PutDocumentWorker.perform_async(Apartment::Tenant.current, id)
    Elasticsearch::PutDocumentWorker.perform_async(Apartment::Tenant.current, parent_id) if parent_id.present? && !parent.draft?
  end

  def should_index_in_elasticsearch?
    Company.current.try(:use_elasticsearch?) && !draft? && business.integrate_elastic?
  end
end
