# frozen_string_literal: true

module Elasticsearch
  class PutDocumentWorker
    include Sidekiq::Worker

    sidekiq_options queue: :elasticsearch_sync, lock: :until_executed, on_conflict: :log, retry: 0, lock_ttl: 1.day

    def perform(tenant, content_id)
      return unless Company.find_by(subdomain: tenant).try(:use_elasticsearch?)

      Apartment::Tenant.switch! tenant

      content = Content.unscoped.where(draft: false).find_by(id: content_id)

      return delete(content_id) if content.blank?

      return unless content.business.integrate_elastic?

      service.put_document(content)
    end

    private

    def service
      @service ||= ElasticSearcherService.new
    end

    def delete(content_id)
      Content.with_discarded.find_by(id: content_id)

      service.delete_document(content_id)
    end
  end
end
