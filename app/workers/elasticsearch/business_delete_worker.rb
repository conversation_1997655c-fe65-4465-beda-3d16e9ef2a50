# frozen_string_literal: true

module Elasticsearch
  class BusinessDeleteWorker
    include Sidekiq::Worker

    sidekiq_options queue: :elasticsearch_setup

    def perform(tenant, business_id)
      Apartment::Tenant.switch!(tenant)

      business = Business.find_by(id: business_id)

      return if business&.integrate_elastic

      service.delete_index business_id
    end

    private

    def service
      @service ||= ElasticSearcherService.new
    end
  end
end
