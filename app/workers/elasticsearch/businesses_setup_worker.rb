# frozen_string_literal: true

module Elasticsearch
  class BusinessesSetupWorker
    include Sidekiq::Worker

    sidekiq_options queue: :elasticsearch_setup

    def perform(tenant)
      return unless validate_tenant(tenant)

      Apartment::Tenant.switch! tenant

      Business.kept.where(integrate_elastic: true).select(:id).find_each do |business|
        Elasticsearch::BusinessSetupWorker.perform_async(tenant, business.id)
      end
    end

    private

    def validate_tenant(tenant)
      Company.find_by(subdomain: tenant).try(:use_elasticsearch?)
    end
  end
end
