# frozen_string_literal: true

module Elasticsearch
  class BusinessSetupWorker
    include Sidekiq::Worker

    sidekiq_options queue: :elasticsearch_setup

    def perform(tenant, business_id)
      return unless validate_tenant(tenant)

      Apartment::Tenant.switch!(tenant)
      service.delete_index(business_id)
      service.create_index(business_id)

      # Indexar todos os conteúdos existentes do business
      enqueue_content_indexing(tenant, business_id)
    end

    def self.job_exists?(tenant, business_id)
      Sidekiq::Queue.new('elasticsearch_setup').each do |job|
        if job.klass == 'Elasticsearch::BusinessSetupWorker' && job.args == [tenant, business_id]
          return true
        end
      end

      Sidekiq::ScheduledSet.new.each do |job|
        if job.klass == 'Elasticsearch::BusinessSetupWorker' && job.args == [tenant, business_id]
          return true
        end
      end

      false
    end

    private

    def service
      @service ||= ElasticSearcherService.new
    end

    def validate_tenant(tenant)
      Company.find_by(subdomain: tenant).try(:use_elasticsearch?)
    end

    def enqueue_content_indexing(tenant, business_id)
      business = Business.find_by(id: business_id)
      return unless business&.integrate_elastic?

      # Enfileirar indexação em lote de todos os conteúdos do business
      Elasticsearch::BulkDocumentPerBusinessWorker.perform_async(tenant, business_id)
    end
  end
end
