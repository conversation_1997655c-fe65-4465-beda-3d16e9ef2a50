# frozen_string_literal: true

module Elasticsearch
  class BulkDocumentPerBusinessWorker
    include Sidekiq::Worker

    sidekiq_options queue: :elasticsearch_setup

    def perform(tenant, business_id)
      return unless Company.find_by(subdomain: tenant).try(:use_elasticsearch?)

      Apartment::Tenant.switch(tenant) do
        business = Business.find_by(id: business_id)
        return unless business&.integrate_elastic?

        Content.unscoped.where(business_id:, draft: false).select(:id).find_each do |content|
          PutDocumentWorker.perform_async(tenant, content.id)
        end
      end
    end
  end
end
