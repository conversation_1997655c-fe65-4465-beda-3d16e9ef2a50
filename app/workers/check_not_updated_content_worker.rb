# frozen_string_literal: true
require_relative '../../lib/elasticsearch/constants'

class CheckNotUpdatedContentWorker
  include Sidekiq::Worker
  include Elasticsearch::Constants

  BATCH_SIZE = 500
  DAYS_TO_SEARCH = 1.year.ago.to_date

  sidekiq_options queue: :elasticsearch_setup, lock: :until_executed, lock_ttl: 6.hours

  def perform(tenant = nil)
    if tenant.blank?
      enqueue_for_all_tenants
    else
      process_tenant(tenant)
    end
  end

  private

  def service
    @service ||= ElasticSearcherService.new
  end

  def enqueue_for_all_tenants
    Company.where(use_elasticsearch: true).pluck(:subdomain).each { |subdomain| self.class.perform_async(subdomain) }
  end

  def process_tenant(tenant)
    return unless tenant_enabled_for_search?(tenant)

    Apartment::Tenant.switch!(tenant)
    process_businesses(tenant)
  end

  def tenant_enabled_for_search?(tenant)
    Company.find_by(subdomain: tenant)&.use_elasticsearch?
  end

  def process_businesses(tenant)
    Business.kept.where(integrate_elastic: true).find_in_batches(batch_size: BATCH_SIZE) do |batch|
      batch.each do |business|
        next unless business.contents.not_draft.exists?

        find_missing_content(business, tenant)
      end
    end
  end

  def find_missing_content(business, tenant)
    Content.unscoped.where(business: business)
      .where('contents.created_at >= ?', DAYS_TO_SEARCH)
      .not_draft.pluck(:id, :updated_at)
      .each_slice(BATCH_SIZE) do |id_timestamp_pairs|
        process_missing_content_batch(id_timestamp_pairs, business, tenant)
      end
  end

  def process_missing_content_batch(id_timestamp_pairs, business, tenant)
    ids = id_timestamp_pairs.map(&:first)
    content_hash = id_timestamp_pairs.to_h

    response = search_in_elasticsearch(ids, tenant, business.id)
    return unless response

    elasticsearch_contents = {}
    response['hits']['hits'].each do |hit|
      content_id = hit['_id'].to_i
      elasticsearch_updated_at = hit['fields'][UPDATED_AT]&.first
      elasticsearch_contents[content_id] = elasticsearch_updated_at
    end

    missing_ids = ids - elasticsearch_contents.keys

    outdated_ids = []
    elasticsearch_contents.each do |content_id, elasticsearch_updated_at|
      database_updated_at = content_hash[content_id]&.iso8601

      if elasticsearch_updated_at != database_updated_at
        outdated_ids << content_id
      end
    end

    ids_to_reindex = missing_ids + outdated_ids
    enqueue_missing_content(ids_to_reindex, tenant) if ids_to_reindex.any?
  end

  def search_in_elasticsearch(ids, tenant, business_id)
    index_name = "#{tenant}-#{business_id}"

    unless index_exists?(index_name)
      Rails.logger.warn "Elasticsearch index '#{index_name}' does not exist. Skipping search and enqueueing all content for indexing."
      enqueue_missing_content(ids, tenant)
      return nil
    end

    service.client.search(
      index: index_name,
      body: {
        query: { bool: { must: [{ terms: { "#{CONTENT_ID}.keyword": ids } }] } },
        fields: [CONTENT_ID, UPDATED_AT], _source: false, size: BATCH_SIZE
      }
    )
  rescue Elastic::Transport::Transport::Errors::NotFound => e
    Rails.logger.warn "Elasticsearch index '#{index_name}' not found during search: #{e.message}"
    enqueue_missing_content(ids, tenant)
    nil
  rescue StandardError => e
    Rails.logger.error "Error searching in Elasticsearch for index '#{index_name}': #{e.message}"
    Rails.logger.error "Backtrace: #{e.backtrace.first(5).join("\n")}"
    nil
  end

  def enqueue_missing_content(ids, tenant)
    ids.each do |id|
      Elasticsearch::PutDocumentWorker.perform_async(tenant, id)
    end
  end

  def index_exists?(index_name)
    service.client.indices.exists?(index: index_name)
  rescue StandardError => e
    Rails.logger.error "Error checking if index '#{index_name}' exists: #{e.message}"
    false
  end
end
