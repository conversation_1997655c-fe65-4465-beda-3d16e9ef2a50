module Dashboard
  class BusinessGroupDecorator < Draper::Decorator
    delegate :id, :name, to: :object

    def inactives
      top_doing.sum(&:inactives)
    end

    def pending_count
      top_doing.sum(&:pending_count)
    end

    def done_count
      top_doing.sum(&:done_count)
    end

    def count
      top_doing.sum(&:count)
    end

    def changing_count
      top_doing.sum(&:changing_count)
    end

    def under_review_count
      top_doing.sum(&:under_review_count)
    end

    def waiting_authorization_count
      top_doing.sum(&:waiting_authorization_count)
    end

    def businesses
      object.businesses.not_sub_business.with_permission_for_user(current_user).where(show_on_dashboard: true)
    end

    def top_doing
      @top_doing ||= Dashboard::BusinessDecorator.decorate_collection(businesses).sort_by(&:pending_count).reverse
    end

    delegate :current_user, to: :h
  end
end
