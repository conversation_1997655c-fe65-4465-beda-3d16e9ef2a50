class FieldGroupedByStepSearcher
  def initialize(options = {})
    @options = options
    @business = Business.find options[:id]

    prepare_to_dependent_field_rule if is_dependent_field_rule?
  end

  def results
    @results = fields_for_business(@business.id)

    return @results unless get_sub_business_fields?

    # Acha o business pai atraves do campo de referencia de subbusiness
    reference_subbusiness_field = Field.kept.find_by(reference_sub_business_id: @business.id)&.template&.steps&.first&.business_id

    raise StandardError, I18n.t('activerecord.errors.messages.destruction_of_subbusiness.must_be_linked_to_a_parent') if reference_subbusiness_field.nil?

    @results + fields_for_business(reference_subbusiness_field)
  end

  def fields_for_business(business_id)
    company_enable_internationalization = Companies::EnableInternationalizationQuery.call
    business = Business.includes(:steps).find(business_id)
    have_parent_business = business.parent.present?
    key_field_ids = business.key_field_ids

    business.active_steps.map do |step|
      fields = Field.kept.for_step(step.id).order('step_templates.order', :order)
      fields = fields.where.not(id: key_field_ids) if except_pk? && key_field_ids.present?

      fields.each do |field|
        field.tap do |f|
          f.label = f.translated_attribute('label', skip_translation?, company_enable_internationalization)
          f.tooltip = f.translated_attribute('tooltip', skip_translation?, company_enable_internationalization)
        end
      end

      {
        step_name: "#{step.business.name} - #{step.name}",
        have_parent_business: have_parent_business,
        step_id: step.id,
        fields: fields,
        key_field_ids: key_field_ids
      }
    end
  end

  def prepare_to_dependent_field_rule
    @options[:parent_business_fields] = @business.sub_business?
  end

  def get_sub_business_fields?
    ['true', true].include?(@options[:parent_business_fields])
  end

  def skip_translation?
    ['true', true].include?(@options[:skip_translation])
  end

  def is_dependent_field_rule?
    ['true', true].include?(@options[:to_dependent_field_rule])
  end

  def except_pk?
    ['true', true].include?(@options[:except_pk])
  end
end
