require 'sidekiq-unique-jobs'

Sidekiq.configure_server do |config|
  config.redis = { url: Rails.application.credentials.REDIS_URL }

  config.client_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Client
  end

  config.server_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Server
  end

  SidekiqUniqueJobs::Server.configure(config)

  # Establish a connection to the database using environment_sidekiq config
  if defined?(ActiveRecord::Base)
    spec = "#{Rails.env}_sidekiq"
    if Rails.application.config.database_configuration.key?(spec)
      ActiveRecord::Base.establish_connection(spec.to_sym)
    else
      ActiveRecord::Base.establish_connection # Use default connection
    end
  end
end

Sidekiq.configure_client do |config|
  config.redis = { url: Rails.application.credentials.REDIS_URL }

  config.client_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Client
  end
end
