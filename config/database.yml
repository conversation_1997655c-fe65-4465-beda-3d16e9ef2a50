default: &default
  adapter: postgresql
  encoding: unicode

  host:     <%= ENV['POSTGRES_HOST'] %>
  username: <%= ENV['POSTGRES_USERNAME'] %>
  password: <%= ENV['POSTGRES_PASSWORD'] %>
  database: <%= ENV['POSTGRES_DATABASE'] %>

  pool: 10
  checkout_timeout: 10

  schema_search_path: "public,shared_extensions"

default_sidekiq: &default_sidekiq
  <<: *default
  username: <%= ENV['SIDEKIQ_POSTGRES_USERNAME'] %>
  pool: 20

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  host:     db
  username: postgres
  password: 93pbf34K8p7A
  database: fourmdg_test<%= ENV['TEST_ENV_NUMBER'] %>


development:
  <<: *default
  host:     db
  username: postgres
  password: 93pbf34K8p7A
  database: fourmdg_development


sandbox:
  <<: *default
  pool: 10

sandbox_sidekiq:
  <<: *default_sidekiq


uat:
  <<: *default
  pool: 50

uat_sidekiq:
  <<: *default_sidekiq


staging:
  <<: *default
  pool: 50

staging_sidekiq:
  <<: *default_sidekiq


production:
  <<: *default
  pool: 100

production_sidekiq:
  <<: *default_sidekiq


academy:
  <<: *default
  pool: 10

academy_sidekiq:
  <<: *default_sidekiq
