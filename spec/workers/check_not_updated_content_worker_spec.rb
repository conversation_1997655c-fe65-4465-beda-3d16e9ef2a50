require 'rails_helper'

RSpec.describe CheckNotUpdatedContentWorker, type: :worker do
  describe 'perform' do
    let(:service) { double(:service, count_index: { 'count' => 1 }) }

    subject { described_class.new.perform(Apartment::Tenant.current) }

    before { allow(ElasticSearcherService).to receive(:new).and_return(service) }

    context 'when the company does not allow elasticsearch indexing' do
      before { Company.current.update!(use_elasticsearch: false) }

      it 'does nothing' do
        expect(service).not_to receive(:count_index)
        expect(Elasticsearch::PutDocumentWorker).not_to receive(:perform_async)

        subject
      end
    end

    context 'when the company allows elasticsearch indexing' do
      let!(:business) { create(:business, :with_dependencies) }
      let(:field) { create(:field, :with_dependencies) }
      let(:step) { create(:step, name: 'main', business: business) }
      let(:field) { create(:field, :with_dependencies) }

      before { Company.current.update!(use_elasticsearch: true) }

      context 'when the businesses do not have any active content' do
        it 'does not check the document count on elasticsearch' do
          expect(service).not_to receive(:count_index)

          subject
        end
      end

      context 'when the businesses have active contents' do
        let(:content) { create(:content, :with_dependencies, business: business) }
        let!(:answer) { create(:answer, :completed, step: step, content: content, values: { field.id => '22' }) }

        context 'when the document count is lower than active contents count' do
          let(:service) { double(:service, count_index: { 'count' => 0 }, client: elasticsearch_client) }
          let(:elasticsearch_indices) { double(:elasticsearch_indices, exists?: true) }
          let(:elasticsearch_client) { double(:elasticsearch_client, search: search_result, indices: elasticsearch_indices) }
          let(:search_result) do
            {
              'hits' => {
                'hits' => [],
                'total' => { 'value' => 0 }
              }
            }
          end

          context 'and the content is not in the scheduled, retry or dead sets' do
            it 'enqueues the missing content document index' do
              expect(Elasticsearch::PutDocumentWorker).to receive(:perform_async).with(Apartment::Tenant.current, content.id)

              subject
            end
          end

          context 'and the content is not in the scheduled, retry or dead sets' do
            let(:sidekiq_job) { double(:sidekiq_job, args: [Apartment::Tenant.current, 'abc123'], klass: 'Elasticsearch::PutDocumentWorker') }
            let(:sidekiq_set) { double(:sidekiq_set, scan: [sidekiq_job]) }

            before { allow(Sidekiq::ScheduledSet).to receive(:new).and_return(sidekiq_set) }

            it 'enqueues the missing content document index' do
              expect(Elasticsearch::PutDocumentWorker).to receive(:perform_async).with(Apartment::Tenant.current, content.id)

              subject
            end
          end
        end

        context 'when the elasticsearch index does not exist' do
          let(:service) { double(:service, count_index: { 'count' => 0 }, client: elasticsearch_client) }
          let(:elasticsearch_indices) { double(:elasticsearch_indices, exists?: false) }
          let(:elasticsearch_client) { double(:elasticsearch_client, indices: elasticsearch_indices) }

          it 'enqueues all content for indexing when index does not exist' do
            expect(Elasticsearch::PutDocumentWorker).to receive(:perform_async).with(Apartment::Tenant.current, content.id)

            subject
          end

          it 'does not attempt to search in elasticsearch when index does not exist' do
            expect(elasticsearch_client).not_to receive(:search)

            subject
          end
        end

        context 'when elasticsearch search raises an error' do
          let(:service) { double(:service, count_index: { 'count' => 0 }, client: elasticsearch_client) }
          let(:elasticsearch_indices) { double(:elasticsearch_indices, exists?: true) }
          let(:elasticsearch_client) { double(:elasticsearch_client, indices: elasticsearch_indices) }

          before do
            allow(elasticsearch_client).to receive(:search).and_raise(Elastic::Transport::Transport::Errors::NotFound.new)
          end

          it 'handles the error gracefully and enqueues content for indexing' do
            expect(Elasticsearch::PutDocumentWorker).to receive(:perform_async).with(Apartment::Tenant.current, content.id)

            subject
          end
        end
      end
    end
  end
end
