require 'rails_helper'

RSpec.describe Elasticsearch::BusinessSetupWorker, type: :worker do
  describe 'perform' do
    let!(:field1) { create(:field, :with_dependencies) }
    let!(:field2) { create(:field, :with_dependencies) }
    let!(:field3) { create(:field, :with_dependencies) }
    let!(:field4) { create(:field, :with_dependencies, deleted_at: Time.zone.now) }

    let(:service) { double(:service, delete_index: nil, create_index: nil) }

    subject { described_class.new }

    before do
      allow(ElasticSearcherService).to receive(:new).and_return(service)
    end

    context 'when the company does not allow elasticsearch indexing' do
      before { Company.current.update!(use_elasticsearch: false) }

      it 'does nothing' do
        expect(Apartment::Tenant).to_not receive(:switch!).with(Apartment::Tenant.current)

        subject.perform(Apartment::Tenant.current, nil)
      end
    end

    context 'when the company allows elasticsearch indexing' do
      subject { described_class.new.perform(Apartment::Tenant.current, @business1.id) }

      before do
        Company.current.update!(use_elasticsearch: true)
        @business1 = create(:business, :with_dependencies, integrate_elastic: true)

        @business1.steps.create!(name: 'Foo', step_templates: [build(:step_template, template: field1.template)])
        @business1.steps.create!(name: 'Bar', step_templates: [build(:step_template, template: field2.template)])
        @business1.steps.create!(name: 'Barz', step_templates: [build(:step_template, template: field4.template)])
      end

      it 'initializes a new ElasticSearcherService' do
        expect(ElasticSearcherService).to receive(:new).and_return(service)

        subject
      end

      it 'deletes the index' do
        expect(service).to receive(:delete_index).with(@business1.id)

        subject
      end

      it 'creates the index' do
        expect(service).to receive(:create_index).with(@business1.id)

        subject
      end

      it 'enqueues bulk content indexing' do
        expect(Elasticsearch::BulkDocumentPerBusinessWorker).to receive(:perform_async).with(Apartment::Tenant.current, @business1.id)

        subject
      end
    end
  end

  describe '.job_exists?' do
    let(:tenant) { 'test_tenant' }
    let(:business_id) { 123 }

    context 'when job exists in queue' do
      before do
        allow_any_instance_of(Sidekiq::Queue).to receive(:each).and_yield(
          double(klass: 'Elasticsearch::BusinessSetupWorker', args: [tenant, business_id])
        )
      end

      it 'returns true' do
        expect(described_class.job_exists?(tenant, business_id)).to be true
      end
    end

    context 'when job exists in scheduled set' do
      before do
        allow_any_instance_of(Sidekiq::Queue).to receive(:each)
        allow_any_instance_of(Sidekiq::ScheduledSet).to receive(:each).and_yield(
          double(klass: 'Elasticsearch::BusinessSetupWorker', args: [tenant, business_id])
        )
      end

      it 'returns true' do
        expect(described_class.job_exists?(tenant, business_id)).to be true
      end
    end

    context 'when job does not exist' do
      before do
        allow_any_instance_of(Sidekiq::Queue).to receive(:each)
        allow_any_instance_of(Sidekiq::ScheduledSet).to receive(:each)
      end

      it 'returns false' do
        expect(described_class.job_exists?(tenant, business_id)).to be false
      end
    end
  end
end
