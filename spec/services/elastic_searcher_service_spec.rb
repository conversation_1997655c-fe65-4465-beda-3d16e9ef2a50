require 'rails_helper'

RSpec.describe ElasticSearcherService, type: :service do
  let(:client) { double(:client, index: true, ssl: double(:ssl).as_null_object) }
  let(:multi_match_param1) { { query: query, analyzer: 'text_analyzer', fuzziness: Elasticsearch::Constants::SEARCH_FUZZINESS, fields: [@field1.elasticsearch_name, @field2.elasticsearch_name] } }
  let(:multi_match_param2) { { query: query, analyzer: 'text_analyzer', fuzziness: Elasticsearch::Constants::SEARCH_FUZZINESS, fields: [@field1.elasticsearch_name] } }
  let(:source_param1) { [Elasticsearch::Constants::BUSINESS_ID, Elasticsearch::Constants::BUSINESS_NAME, Elasticsearch::Constants::CONTENT_ID, @field1.elasticsearch_name, @field2.elasticsearch_name] }
  let(:source_param2) { [Elasticsearch::Constants::BUSINESS_ID, Elasticsearch::Constants::BUSINESS_NAME, Elasticsearch::Constants::CONTENT_ID, @field1.elasticsearch_name] }

  describe '#client' do
    context 'when using API key authentication' do
      before do
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_url).and_return('http://ofooba')
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_api_key).and_return('test-api-key')
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_user).and_return(nil)
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_password).and_return(nil)
      end

      it 'initializes a new elasticsearch client with API key' do
        expect(Elasticsearch::Client).to receive(:new).with({
          url: 'http://ofooba',
          log: true,
          api_key: 'test-api-key'
        }).and_return(client)

        described_class.new.client
      end
    end

    context 'when using basic authentication' do
      before do
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_url).and_return('http://ofooba')
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_api_key).and_return(nil)
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_user).and_return('foo')
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_password).and_return('barrr')
      end

      it 'initializes a new elasticsearch client with user/password' do
        expect(Elasticsearch::Client).to receive(:new).with({
          url: 'http://ofooba',
          log: true,
          user: 'foo',
          password: 'barrr'
        }).and_return(client)

        described_class.new.client
      end
    end

    context 'when no authentication is configured' do
      before do
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_url).and_return('http://ofooba')
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_api_key).and_return(nil)
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_user).and_return(nil)
        allow(Rails.application.credentials).to receive(:[]).with(:elasticsearch_password).and_return(nil)
      end

      it 'initializes a new elasticsearch client without authentication' do
        expect(Elasticsearch::Client).to receive(:new).with({
          url: 'http://ofooba',
          log: true
        }).and_return(client)

        described_class.new.client
      end
    end

    it 'returns client' do
      allow(Elasticsearch::Client).to receive(:new).and_return(client)

      result = described_class.new.client
      expect(result).to eq client
    end
  end

  describe '#create_index' do
    let(:indices) { double(:indices, create: true) }
    let(:business) { create(:business, :with_dependencies) }

    let(:body) do
      {
        mappings: {
          properties: {}
        },
        settings: {
          index: {
            analysis: {
              analyzer: {
                text_analyzer: { type: 'custom', tokenizer: 'standard', filter: %w[lowercase asciifolding] }
              },
              char_filter: {
                accent_filter: {
                  type: 'mapping',
                  mappings: ['ç=>c', 'Ç=>C', 'á=>a', 'é=>e', 'í=>i', 'ó=>o', 'ú=>u', 'ý=>y', 'Á=>A', 'É=>E', 'Í=>I', 'Ó=>O', 'Ú=>U', 'Ý=>Y', 'à=>a', 'è=>e', 'ì=>i', 'ò=>o', 'ù=>u', 'À=>A', 'È=>E', 'Ì=>I', 'Ò=>O', 'Ù=>U', 'ã=>a', 'õ=>o', 'ñ=>n', 'ä=>a', 'ë=>e', 'ï=>i', 'ö=>o', 'ü=>u', 'ÿ=>y', 'Ä=>A', 'Ë=>E', 'Ï=>I', 'Ö=>O', 'Ü=>U', 'Ã=>A', 'Õ=>O', 'Ñ=>N', 'â=>a', 'ê=>e', 'î=>i', 'ô=>o', 'û=>u', 'Â=>A', 'Ê=>E', 'Î=>I', 'Ô=>O', 'Û=>U']
                },
                uppercase_filter: {
                  type: 'pattern_replace',
                  pattern: '([A-Z])',
                  replacement: 'U'
                },
                lowercase_filter: {
                  type: 'pattern_replace',
                  pattern: '([a-z])',
                  replacement: 'L'
                },
                number_filter: {
                  type: 'pattern_replace',
                  pattern: '([0-9])',
                  replacement: 'N'
                },
                whitespace_filter: {
                  type: 'pattern_replace',
                  pattern: '([\s])',
                  replacement: 'S'
                }
              },
              normalizer: {
                keyword_normalizer: { type: 'custom', filter: %w[lowercase asciifolding] },
                pattern_normalizer: { type: 'custom', char_filter: %w[accent_filter uppercase_filter lowercase_filter number_filter whitespace_filter] }
              }
            },
            "mapping.total_fields.limit": Elasticsearch::Constants::MAPPING_TOTAL_FIELDS_LIMIT
          }
        }
      }
    end

    subject {
      described_class.new.create_index(business.id)
    }

    before do
      allow(Elasticsearch::Client).to receive(:new).and_return(client)
      allow(client).to receive(:indices).and_return(indices)
      allow(indices).to receive(:exists?).with(index: "#{Apartment::Tenant.current}-#{business.id}").and_return(false)
    end


    it 'creates the index' do
      expect(indices).to receive(:create).with(index: "#{Apartment::Tenant.current}-#{business.id}", body: body)

      subject
    end
  end

  describe '#delete_index' do
    let(:indices) { double(:indices, delete: true) }
    let(:business) { create(:business, :with_dependencies) }

    subject { described_class.new.delete_index(business.id) }

    before do
      allow(Elasticsearch::Client).to receive(:new).and_return(client)
      allow(client).to receive(:indices).and_return(indices)
    end

    it 'deletes the index' do
      expect(indices).to receive(:delete).with(index: "#{Apartment::Tenant.current}-#{business.id}", ignore_unavailable: true)

      subject
    end
  end

  describe '#count_index' do
    let(:business) { create(:business, :with_dependencies) }

    subject { described_class.new.count_index(business.id, query: { bool: { must: [{ terms: { '__CONTENT_ID.keyword': ['foo'] } }] } }) }

    before do
      allow(Elasticsearch::Client).to receive(:new).and_return(client)
    end

    it 'counts the index size' do
      expect(client).to receive(:count).with(index: "#{Apartment::Tenant.current}-#{business.id}", body: { query: { bool: { must: [{ terms: { '__CONTENT_ID.keyword': ['foo'] } }] } } })

      subject
    end
  end

  describe '#put_document' do
    let!(:field1) { create(:field, :with_dependencies, label: 'field1', created_at: 2.days.ago) }
    let!(:field2) { create(:field, :with_dependencies, label: 'field2', created_at: 1.day.ago) }
    let!(:field3) { create(:field, :with_dependencies, label: 'field3', created_at: Time.zone.now) }
    let!(:answer) { create(:answer, :completed, :with_dependencies, position: 0, values: { field1.id => '123', field2.id => '444', field3.id => 232.22 }) }
    let(:content) { answer.content }
    let(:document) do
      {
        "#{Elasticsearch::Constants::CONTENT_ID}" => answer.content.id,
        "#{Elasticsearch::Constants::BUSINESS_ID}" => answer.content.business_id,
        "#{Elasticsearch::Constants::NOTE}" => answer.content.note,
        "#{Elasticsearch::Constants::BUSINESS_NAME}" => answer.content.business.name,
        "#{Elasticsearch::Constants::CONTENT_STATUS}" => answer.content.translated_status,
        "#{Elasticsearch::Constants::DELETION_REASON}" => answer.content.deletion_reason.to_s,
        "#{Elasticsearch::Constants::UPDATED_BY_NAME}" => answer.content.last_update_by.try(:name),
        "#{Elasticsearch::Constants::DELETED_AT}" => answer.content.deleted_at.try(:iso8601),
        "#{Elasticsearch::Constants::CREATED_AT}" => answer.content.created_at.iso8601,
        "#{Elasticsearch::Constants::UPDATED_AT}" => answer.content.updated_at.iso8601,
        'answers.0.updated_at' => answer.updated_at.iso8601,
        'answers.0.authorized_at' => answer.authorized_at.try(:iso8601), 'answers.0.available_at' => answer.available_at.try(:iso8601),
        'answers.0.concluded_at' => answer.concluded_at.try(:iso8601), 'answers.0.filled_at' => answer.filled_at.try(:iso8601),
        'answers.0.first_fill_at' => answer.first_fill_at.try(:iso8601),
        'answers.0.user_name' => answer.user.try(:name), 'answers.0.authorizer_name' => answer.authorizer.try(:name),
        'answers.0.status' => 'concluído',
        'answers.0.created_by_name' => answer.created_by.try(:name)
      }
    end

    subject { described_class.new.put_document(content) }

    before do
      allow(Elasticsearch::Client).to receive(:new).and_return(client)
    end

    it 'indexes the document' do
      Field.for_business(content.business_id).each do |field|
        document[field.elasticsearch_name] = answer.values[field.id]
      end
      expect(client).to receive(:index).with(index: "#{Apartment::Tenant.current}-#{answer.content.business.id}", id: content.id, body: document)

      subject
    end

    context 'when the field is empty' do
      let!(:field1) { create(:field, :with_dependencies, label: 'field1', created_at: 2.days.ago, type: :date) }
      let(:answer) { create(:answer, :completed, :with_dependencies, position: 0, values: { field1.id => '', field2.id => '444', field3.id => 232.22 }) }
      let(:document) do
        {
          "#{ElasticSearcherService::CONTENT_ID}" => answer.content.id,
          "#{ElasticSearcherService::BUSINESS_ID}" => answer.content.business_id,
          "#{ElasticSearcherService::NOTE}" => answer.content.note,
          "#{ElasticSearcherService::BUSINESS_NAME}" => answer.content.business.name,
          "#{ElasticSearcherService::CONTENT_STATUS}" => answer.content.translated_status,
          "#{ElasticSearcherService::DELETION_REASON}" => answer.content.deletion_reason.to_s,
          "#{ElasticSearcherService::UPDATED_BY_NAME}" => answer.content.last_update_by.try(:name),
          "#{ElasticSearcherService::DELETED_AT}" => answer.content.deleted_at.try(:iso8601),
          "#{ElasticSearcherService::CREATED_AT}" => answer.content.created_at.iso8601,
          "#{Elasticsearch::Constants::UPDATED_AT}" => answer.content.updated_at.iso8601,
          'answers.0.updated_at' => answer.updated_at.iso8601,
          'answers.0.authorized_at' => answer.authorized_at.try(:iso8601), 'answers.0.available_at' => answer.available_at.try(:iso8601),
          'answers.0.concluded_at' => answer.concluded_at.try(:iso8601), 'answers.0.filled_at' => answer.filled_at.try(:iso8601),
          'answers.0.first_fill_at' => answer.first_fill_at.try(:iso8601),
          'answers.0.status' => 'concluído',
          'answers.0.user_name' => answer.user.try(:name), 'answers.0.authorizer_name' => answer.authorizer.try(:name), 'answers.0.created_by_name' => answer.created_by.try(:name)
        }
      end

      it 'indexes the document sending \'nil\' instead of the empty string' do
        Field.for_business(content.business_id).each do |field|
          document[field.elasticsearch_name] = answer.values[field.id]
        end
        expect(client).to receive(:index).with(index: "#{Apartment::Tenant.current}-#{answer.content.business.id}", id: content.id, body: document)

        subject
      end
    end
  end

  describe '#put_documents' do
    let!(:answer1) { create(:answer, :completed, :with_dependencies, position: 0, values: { field1.id => '123', field2.id => '444', field3.id => 232.22 }) }
    let!(:answer2) { create(:answer, :completed, :with_dependencies, position: 0, values: { field1.id => '321', field2.id => '888', field3.id => 333.33 }) }
    let!(:answer3) { create(:answer, :completed, :with_dependencies, position: 0, values: { field1.id => '321', field2.id => '888', field3.id => 333.33 }) }
    let!(:field1) { create(:field, :with_dependencies, label: 'field1', created_at: 2.days.ago) }
    let!(:field2) { create(:field, :with_dependencies, label: 'field2', created_at: 1.day.ago) }
    let!(:field3) { create(:field, :with_dependencies, label: 'field3', created_at: Time.zone.now) }
    let(:documents) do
      [
        {
          index: {
            _id: answer1.content_id, data: {
              "#{Elasticsearch::Constants::CONTENT_ID}": answer1.content.id,
              "#{Elasticsearch::Constants::BUSINESS_ID}" => answer1.content.business_id,
              "#{Elasticsearch::Constants::NOTE}" => answer1.content.note,
              "#{Elasticsearch::Constants::BUSINESS_NAME}" => answer1.content.business.name,
              "#{Elasticsearch::Constants::CONTENT_STATUS}" => answer1.content.translated_status,
              "#{Elasticsearch::Constants::DELETION_REASON}" => answer1.content.deletion_reason.to_s,
              "#{Elasticsearch::Constants::UPDATED_BY_NAME}" => answer1.content.last_update_by.try(:name),
              "#{Elasticsearch::Constants::DELETED_AT}" => answer1.content.deleted_at.try(:iso8601),
              "#{Elasticsearch::Constants::CREATED_AT}" => answer1.content.created_at.iso8601,
              'answers.0.updated_at' => answer1.updated_at.iso8601,
              'answers.0.authorized_at' => answer1.authorized_at.try(:iso8601), 'answers.0.available_at' => answer1.available_at.try(:iso8601),
              'answers.0.concluded_at' => answer1.concluded_at.try(:iso8601), 'answers.0.filled_at' => answer1.filled_at.try(:iso8601),
              'answers.0.first_fill_at' => answer1.first_fill_at.try(:iso8601),
              'answers.0.status' => 'concluído',
              'answers.0.user_name' => answer1.user.try(:name), 'answers.0.authorizer_name' => answer1.authorizer.try(:name), 'answers.0.created_by_name' => answer1.created_by.try(:name)
            }
          }
        }, {
          index: {
            _id: answer2.content_id, data: {
              "#{ElasticSearcherService::CONTENT_ID}" => answer2.content.id,
              "#{ElasticSearcherService::BUSINESS_ID}" => answer2.content.business_id,
              "#{ElasticSearcherService::NOTE}" => answer2.content.note,
              "#{ElasticSearcherService::BUSINESS_NAME}" => answer2.content.business.name,
              "#{ElasticSearcherService::CONTENT_STATUS}" => answer2.content.translated_status,
              "#{ElasticSearcherService::DELETION_REASON}" => answer2.content.deletion_reason.to_s,
              "#{ElasticSearcherService::UPDATED_BY_NAME}" => answer2.content.last_update_by.try(:name),
              "#{ElasticSearcherService::DELETED_AT}" => answer2.content.deleted_at.try(:iso8601),
              "#{ElasticSearcherService::CREATED_AT}" => answer2.content.created_at.iso8601,
              'answers.0.updated_at' => answer2.updated_at.iso8601,
              'answers.0.authorized_at' => answer2.authorized_at.try(:iso8601), 'answers.0.available_at' => answer2.available_at.try(:iso8601),
              'answers.0.concluded_at' => answer2.concluded_at.try(:iso8601), 'answers.0.filled_at' => answer2.filled_at.try(:iso8601),
              'answers.0.first_fill_at' => answer2.first_fill_at.try(:iso8601),
              'answers.0.status' => 'concluído',
              'answers.0.user_name' => answer2.user.try(:name), 'answers.0.authorizer_name' => answer2.authorizer.try(:name), 'answers.0.created_by_name' => answer2.created_by.try(:name)
            }
          }
        },
        {
          index: {
            _id: answer3.content_id, data: {
              "#{ElasticSearcherService::CONTENT_ID}": answer3.content.id, "#{ElasticSearcherService::BUSINESS_ID}": answer3.content.business_id,
              "#{ElasticSearcherService::NOTE}": answer3.content.note, "#{ElasticSearcherService::BUSINESS_NAME}": answer3.content.business.name,
              "#{ElasticSearcherService::CONTENT_STATUS}": answer3.content.translated_status,
              "#{ElasticSearcherService::DELETION_REASON}": answer3.content.deletion_reason.to_s,
              "#{ElasticSearcherService::UPDATED_BY_NAME}": answer3.content.last_update_by.try(:name),
              "#{ElasticSearcherService::DELETED_AT}": answer3.content.deleted_at.try(:iso8601),
              "#{ElasticSearcherService::CREATED_AT}": answer3.content.created_at.iso8601,
              'answers.0.updated_at' => answer3.updated_at.iso8601,
              'answers.0.authorized_at' => answer3.authorized_at.try(:iso8601), 'answers.0.available_at' => answer3.available_at.try(:iso8601),
              'answers.0.concluded_at' => answer3.concluded_at.try(:iso8601), 'answers.0.filled_at' => answer3.filled_at.try(:iso8601),
              'answers.0.first_fill_at' => answer3.first_fill_at.try(:iso8601),
              'answers.0.status' => 'concluído',
              'answers.0.user_name' => answer3.user.try(:name), 'answers.0.authorizer_name' => answer3.authorizer.try(:name), 'answers.0.created_by_name' => answer3.created_by.try(:name)
            }
          }
        }
      ]
    end

    subject { described_class.new.put_documents([answer1.content_id, answer2.content_id, answer3.content_id]) }

    before do
      allow(Elasticsearch::Client).to receive(:new).and_return(client)
    end

    context 'when the contents are not from the same business' do
      it 'raises a service error' do
        expect { subject }.to raise_error(Elasticsearch::BaseService::ServiceError, 'Os contents devem pertencer ao mesmo negócio')
      end
    end

    context 'when the contents are from the same business' do
      let(:business) { create(:business, :with_dependencies) }
      # let(:answer1) { create(:answer, :completed, :with_dependencies, position: 0, values: { field1.id => '123', field2.id => '444', field3.id => 232.22 }) }
      # let(:answer2) { create(:answer, :completed, :with_dependencies, position: 0, values: { field1.id => '321', field2.id => '888', field3.id => 333.33 }) }
      # let(:answer3) { create(:answer, :completed, :with_dependencies, position: 0, values: { field1.id => '222', field2.id => '999', field3.id => 777.77 }) }

      before do
        answer1.content.update_attribute(:business_id, business.id)
        answer2.content.update_attribute(:business_id, business.id)
        answer3.content.update_attribute(:business_id, business.id)

        answer3.content.update_attribute(:deleted_at, Time.zone.now)
      end

      it 'indexes the active documents' do
        expect(client).to receive(:bulk) do |args|
          expect(args[:index]).to eq("#{Apartment::Tenant.current}-#{answer1.content.business_id}")
          expect(args[:body].map { |d| d[:index][:_id] }).to include(answer1.content_id, answer2.content_id)
        end

        subject
      end

      context 'when the content belongs to a discarded business' do
        before{ business.discard! }

        let(:error_message) { "Os ids '#{[answer1.content_id, answer2.content_id, answer3.content_id].join(' | ')}' não pertencem a nenhum negócio ativo ou não existem"}

        it 'raises a service error' do
          expect { subject }.to raise_error(Elasticsearch::BaseService::ServiceError, error_message)
        end
      end
    end
  end

  describe '#delete_document' do
    let(:answer) { create(:answer, :completed, :with_dependencies) }
    let(:content) { answer.content }
    let(:document) { {} }

    subject { described_class.new.delete_document(content.id) }

    before do
      allow(Elasticsearch::Client).to receive(:new).and_return(client)
    end

    it 'deletes the document' do
      expect(client).to receive(:delete).with(index: "#{Apartment::Tenant.current}-#{content.business_id}", id: content.id)

      subject
    end

    context 'when the content does not exist' do
      let(:random_id) { SecureRandom.uuid }
      subject { described_class.new.delete_document(random_id) }

      it 'deletes the document by query' do
        expect(client).to receive(:delete_by_query).with(index: "#{Apartment::Tenant.current}-*", body: { query: { match: { "#{Elasticsearch::Constants::CONTENT_ID}" => random_id } } })

        subject
      end
    end
  end

  describe '#search' do
    let(:business1) { create(:business, :with_dependencies, created_at: 2.days.ago) }
    let(:business2) { create(:business, :with_dependencies, created_at: 1.day.ago) }
    let(:query) { 'Francesca' }
    let(:field_names) { [@field1.label] }

    subject { described_class.new(parameters).search }

    before do
      @field1 = create(:field, :with_dependencies, label: 'field1', created_at: 2.days.ago)
      @field2 = create(:field, :with_dependencies, label: 'field2', created_at: 1.day.ago)

      create(:step_template, template: @field1.template, step: create(:step, business: business1))
      create(:step_template, template: @field2.template, step: create(:step, business: business2))

      allow(Elasticsearch::Client).to receive(:new).and_return(client)
    end

    context 'with simplified search' do
      let(:parameters) { { business_id: business1.id, query: query, field_names: field_names } }
      let(:body) do
        {
          query: {
            bool: {
              must: [
                { multi_match: multi_match_param2 },
                bool: {
                  should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business1.id } }]
                }
              ]
            }
          }, size: 100, from: 0, track_total_hits: true, _source: source_param2
        }
      end

      context 'when the query is not present' do
        let(:parameters) { { business_id: business1.id, query: nil, field_names: field_names } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  bool: {
                    should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business1.id } }]
                  }
                ]
              }
            }, size: 100, from: 0, track_total_hits: true, _source: source_param2
          }
        end

        it 'searches using only the business id' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end
      end

      context 'when the query is present' do
        it 'searches using the query text' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end
      end

      context 'with from parameter' do
        let(:parameters) { { business_id: business1.id, query: query, field_names: field_names, from: 100 } }
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { multi_match: multi_match_param2 },
                  bool: {
                    should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business1.id } }]
                  }
                ]
              }
            }, size: 100, from: 100, track_total_hits: true, _source: source_param2
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        it 'search' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end

        context 'when hits.length is equal to page size' do
          it 'returns next_page' do
            expect(subject[:next_page]).to eq 200
          end
        end

        context 'when hits.length is lower to page size' do
          let(:hits) { (1..5).map { |index| { a: index } } }

          it 'returns next_page' do
            expect(subject[:next_page]).to be_nil
          end
        end
      end
    end

    context 'with advanced search' do
      let(:query) { "#{@field1.elasticsearch_name}:Franc*" }
      let(:parameters) { { business_id: business1.id, query: query, field_names: field_names, kind: 'advanced' }.with_indifferent_access }
      let(:body) do
        {
          query: {
            bool: {
              must: [
                { query_string: { query: query, analyzer: 'standard' } },
                bool: {
                  should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business1.id } }]
                }
              ]
            }
          }, size: 100, from: 0, track_total_hits: true, _source: source_param2
        }
      end

      context 'when the query is not present' do
        let(:parameters) { { business_id: business1.id, query: nil, field_names: field_names, kind: 'advanced' }.with_indifferent_access }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  bool: {
                    should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business1.id } }]
                  }
                ]
              }
            }, size: 100, from: 0, track_total_hits: true, _source: source_param2
          }
        end

        it 'searches using only the business id' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end
      end

      context 'when the query is present' do
        it 'searches using the query text' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end
      end

      context 'with from parameter' do
        let(:parameters) { { business_id: business1.id, query: query, field_names: field_names, from: 100, kind: 'advanced' }.with_indifferent_access }
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { query_string: { query: query, analyzer: 'standard' } },
                  bool: {
                    should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business1.id } }]
                  }
                ]
              }
            }, size: 100, from: 100, track_total_hits: true, _source: source_param2
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        it 'search' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end

        context 'when hits.length is equal to page size' do
          it 'returns next_page' do
            expect(subject[:next_page]).to eq 200
          end
        end

        context 'when hits.length is lower to page size' do
          let(:hits) { (1..5).map { |index| { a: index } } }

          it 'returns next_page' do
            expect(subject[:next_page]).to be_nil
          end
        end
      end
    end

    context 'with raw search' do
      let(:query) { "#{@field1.elasticsearch_name}:Franc*" }
      let(:query_as_json) { query.to_json}
      let(:parameters) { { business_id: business1.id, query: query_as_json, field_names: field_names, kind: 'raw' }.with_indifferent_access }

      it 'searches using the query as body' do
        expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: query)

        subject
      end

      context 'with from parameter' do
        let(:parameters) { { business_id: business1.id, query: query_as_json, field_names: field_names, from: 100, kind: 'raw' }.with_indifferent_access }
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }

        before { allow(client).to receive(:search).and_return elastic_response }

        it 'search' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: query)

          subject
        end

        context 'when hits.length is equal to page size' do
          it 'returns next_page' do
            expect(subject[:next_page]).to eq 200
          end
        end

        context 'when hits.length is lower to page size' do
          let(:hits) { (1..5).map { |index| { a: index } } }

          it 'returns next_page' do
            expect(subject[:next_page]).to be_nil
          end
        end
      end
    end
  end

  describe '#search_body' do
    let(:business1) { create(:business, :with_dependencies, created_at: 2.days.ago) }
    let(:business2) { create(:business, :with_dependencies, created_at: 1.day.ago) }
    let(:query) { 'Francesca' }
    let(:field_names) { [@field1.label] }
    let(:parameters) { { business_id: business1.id, query: query, field_names: field_names } }
    let(:body) do
      {
        query: {
          bool: {
            must: [
              { multi_match: multi_match_param2 },
              bool: {
                should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business1.id } }]
              }
            ]
          }
        }, size: 100, from: 0, track_total_hits: true, _source: source_param2
      }
    end

    subject { described_class.new(parameters) }

    before do
      @field1 = create(:field, :with_dependencies, label: 'field1', created_at: 2.days.ago)
      @field2 = create(:field, :with_dependencies, label: 'field2', created_at: 1.day.ago)

      create(:step_template, template: @field1.template, step: create(:step, business: business1))
      create(:step_template, template: @field2.template, step: create(:step, business: business2))

      allow(Elasticsearch::Client).to receive(:new).and_return(client)
      allow(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

      subject.search
    end

    it 'returns the current body' do
      expect(subject.search_body).to eq(body)
    end
  end

  describe '#data_profile' do
    let(:business) { create(:business, :with_dependencies) }
    let(:field_names) { [@field1.label, @field2.label] }

    subject { described_class.new(parameters).data_profile }

    before do
      @field1 = create(:field, :with_dependencies, label: 'field1', created_at: 2.days.ago)
      @field2 = create(:field, :with_dependencies, label: 'field2', created_at: 1.day.ago)

      create(:step_template, template: @field1.template, step: create(:step, business: business))
      create(:step_template, template: @field2.template, step: create(:step, business: business))

      allow(Elasticsearch::Client).to receive(:new).and_return(client)
    end

    context 'with simplified search' do
      let(:query) { 'Francesca' }
      let(:parameters) { { business_id: business.id, query: query, field_names: field_names } }
      let(:body) do
        {
          query: {
            bool: {
              must: [
                { multi_match: multi_match_param1 },
                bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
              ]
            }
          },
          track_total_hits: false,
          size: 0, from: 0,
          aggs: {
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::STRING_STAT}" => { string_stats: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_STAT}" => { terms: { field: "#{@field1.elasticsearch_name}.keyword", size: Elasticsearch::Constants::OCCURRENCE_RESULTS } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::STRING_STAT}" => { string_stats: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_STAT}" => { terms: { field: "#{@field2.elasticsearch_name}.keyword", size: ElasticSearcherService::OCCURRENCE_RESULTS } }
          }
        }
      end

      it 'searches using the correct body' do
        expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

        subject
      end

      context 'with from parameter' do
        let(:parameters) { { business_id: business.id, query: query, field_names: field_names, from: 100 } }
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { multi_match: multi_match_param1 },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            track_total_hits: false,
            size: 0, from: 100,
            aggs: {
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::STRING_STAT}" => { string_stats: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_STAT}" => { terms: { field: "#{@field1.elasticsearch_name}.keyword", size: Elasticsearch::Constants::OCCURRENCE_RESULTS } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::STRING_STAT}" => { string_stats: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_STAT}" => { terms: { field: "#{@field2.elasticsearch_name}.keyword", size: Elasticsearch::Constants::OCCURRENCE_RESULTS } }
            }
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        it 'searches using the correct body' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end

        context 'when hits.length is equal to page size' do
          it 'returns next_page' do
            expect(subject[:next_page]).to eq 200
          end
        end

        context 'when hits.length is lower to page size' do
          let(:hits) { (1..5).map { |index| { a: index } } }

          it 'returns next_page' do
            expect(subject[:next_page]).to be_nil
          end
        end
      end
    end

    context 'with advanced search' do
      let(:query) { "#{@field1.elasticsearch_name}:Franc*" }
      let(:parameters) { { business_id: business.id, query: query, field_names: field_names, kind: 'advanced' }.with_indifferent_access }
      let(:body) do
        {
          query: {
            bool: {
              must: [
                { query_string: { query: query, analyzer: 'standard' } },
                bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
              ]
            }
          },
          track_total_hits: false,
          size: 0, from: 0,
          aggs: {
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::STRING_STAT}" => { string_stats: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_STAT}" => { terms: { field: "#{@field1.elasticsearch_name}.keyword", size: 100 } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::STRING_STAT}" => { string_stats: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_STAT}" => { terms: { field: "#{@field2.elasticsearch_name}.keyword", size: 100 } }
          }
        }
      end

      it 'searches using the correct body' do
        expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

        subject
      end

      context 'with from parameter' do
        let(:parameters) { { business_id: business.id, query: query, field_names: field_names, from: 100, kind: 'advanced' }.with_indifferent_access }
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { query_string: { query: query, analyzer: 'standard' } },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            track_total_hits: false,
            size: 0, from: 100,
            aggs: {
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::STRING_STAT}" => { string_stats: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_STAT}" => { terms: { field: "#{@field1.elasticsearch_name}.keyword", size: 100 } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::STRING_STAT}" => { string_stats: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_STAT}" => { terms: { field: "#{@field2.elasticsearch_name}.keyword", size: 100 } }
            }
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        it 'searches using the correct body' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end

        context 'when hits.length is equal to page size' do
          it 'returns next_page' do
            expect(subject[:next_page]).to eq 200
          end
        end

        context 'when hits.length is lower to page size' do
          let(:hits) { (1..5).map { |index| { a: index } } }

          it 'returns next_page' do
            expect(subject[:next_page]).to be_nil
          end
        end
      end
    end
  end

  describe '#mapping_information' do
    let(:business) { create(:business, :with_dependencies) }
    let(:field_names) { [@field1.label, @field2.label] }
    let(:parameters) { { field_names: field_names, business_id: business.id } }
    let(:response) { double(:indices, body: 'foobar') }

    subject { described_class.new(parameters).mapping_information }

    before do
      @field1 = create(:field, :with_dependencies, label: 'Campo com acentuação', created_at: 2.days.ago)
      @field2 = create(:field, :with_dependencies, label: 'Campo com \\ barras', created_at: 1.day.ago)

      create(:step_template, template: @field1.template, step: create(:step, business: business))
      create(:step_template, template: @field2.template, step: create(:step, business: business))

      allow(Elasticsearch::Client).to receive(:new).and_return(client)
      allow(client).to receive(:perform_request).and_return(response)
    end

    context 'when there are no fields to be shown' do
      let(:field_names) { [] }

      before do
        @field1.destroy
        @field2.destroy
      end

      it 'raises a service error' do
        expect { subject }.to raise_error(Elasticsearch::BaseService::ServiceError, 'Esse negócio não tem nenhum campo marcado para visualizar na listagem. Por favor selecione na tela anterior os campos que deseja visualizar')
      end
    end

    context 'when there are fields to be shown' do
      it 'get the fields mapping' do
        expect(client).to receive(:perform_request).with('GET', "#{Apartment::Tenant.current}-#{business.id}/_mapping/field/Campo%20com%20acentua%C3%A7%C3%A3o,Campo%20com%20%5C%20barras")

        subject
      end
    end

    context 'line is larger than 4096 bytes error' do
      before do
        allow(client).to receive(:perform_request).and_raise 'An HTTP line is larger than 4096 bytes'
      end

      it 'returns the errors' do
        expect { subject }.to raise_error(Elasticsearch::BaseService::ServiceError, a_string_including('Muitos campos para serem processados! Por favor filtre os campos a serem exibidos.'))
      end
    end
  end

  describe '#field_stats' do
    let(:business) { create(:business, :with_dependencies) }
    let(:field_names) { [@field1.label, @field2.label] }

    subject { described_class.new(parameters).field_stats }

    before do
      @field1 = create(:field, :with_dependencies, label: 'field1', created_at: 2.days.ago)
      @field2 = create(:field, :with_dependencies, label: 'field2', created_at: 1.day.ago)

      create(:step_template, template: @field1.template, step: create(:step, business: business))
      create(:step_template, template: @field2.template, step: create(:step, business: business))

      allow(Elasticsearch::Client).to receive(:new).and_return(client)
    end

    context 'with simplified search' do
      let(:query) { 'Francesca' }
      let(:parameters) { { business_id: business.id, query: query, field_names: field_names } }
      let(:body) do
        {
          query: {
            bool: {
              must: [
                { multi_match: multi_match_param1 },
                bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
              ]
            }
          },
          track_total_hits: false, size: 0, from: 0,
          aggs: {
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::OCCURRENCE_RESULTS, after: { value: '' } } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.pattern" } } }], size: Elasticsearch::Constants::PATTERN_RESULTS, after: { value: '' } } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::OCCURRENCE_RESULTS, after: { value: '' } } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.pattern" } } }], size: Elasticsearch::Constants::PATTERN_RESULTS, after: { value: '' } } }
          }
        }
      end

      it 'searches using the correct body' do
        expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

        subject
      end

      context 'with from parameter' do
        let(:parameters) { { business_id: business.id, query: query, field_names: field_names, from: 100 } }
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { multi_match: multi_match_param1 },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            track_total_hits: false, size: 0, from: 100,
            aggs: {
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::OCCURRENCE_RESULTS, after: { value: '' } } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.pattern" } } }], size: Elasticsearch::Constants::PATTERN_RESULTS, after: { value: '' } } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::OCCURRENCE_RESULTS, after: { value: '' } } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.pattern" } } }], size: Elasticsearch::Constants::PATTERN_RESULTS, after: { value: '' } } }
            }
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        it 'searches using the correct body' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end

        context 'when hits.length is equal to page size' do
          it 'returns next_page' do
            expect(subject[:next_page]).to eq 200
          end
        end

        context 'when hits.length is lower to page size' do
          let(:hits) { (1..5).map { |index| { a: index } } }

          it 'returns next_page' do
            expect(subject[:next_page]).to be_nil
          end
        end
      end

      context 'with stat parameter' do
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { multi_match: multi_match_param1 },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            track_total_hits: false, size: 0, from: 0,
            aggs: {
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field2.elasticsearch_name}.keyword" } }
            }
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        context 'when the stat exists' do
          let(:parameters) { { business_id: business.id, query: query, field_names: field_names, stat: 'uniqueness' } }

          it 'searches using the correct body' do
            expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

            subject
          end
        end

        context 'when the stat does not exist' do
          let(:parameters) { { business_id: business.id, query: query, field_names: field_names, stat: 'foobar' } }

          it 'raises a service error' do
            expect { subject }.to raise_error(Elasticsearch::BaseService::ServiceError, 'Atributo inválido')
          end
        end
      end

      context 'with pagination key parameter' do
        let(:parameters) { { business_id: business.id, query: query, field_names: field_names, pagination_key: 'ABCFOOBAR' } }
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { multi_match: multi_match_param1 },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            track_total_hits: false, size: 0, from: 0,
            aggs: {
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::OCCURRENCE_RESULTS, after: { value: parameters[:pagination_key] } } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.pattern" } } }], size: Elasticsearch::Constants::PATTERN_RESULTS, after: { value: parameters[:pagination_key] } } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::OCCURRENCE_RESULTS, after: { value: parameters[:pagination_key] } } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.pattern" } } }], size: Elasticsearch::Constants::PATTERN_RESULTS, after: { value: parameters[:pagination_key] } } }
            }
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        it 'searches using the correct body' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end
      end
    end

    context 'with advanced search' do
      let(:query) { "#{@field1.elasticsearch_name}:Franc*" }
      let(:parameters) { { business_id: business.id, query: query, field_names: field_names, kind: 'advanced' }.with_indifferent_access }
      let(:body) do
        {
          query: {
            bool: {
              must: [
                { query_string: { query: query, analyzer: 'standard' } },
                bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
              ]
            }
          },
          track_total_hits: false, size: 0, from: 0,
          aggs: {
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field1.elasticsearch_name}.keyword" } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: 100, after: { value: '' } } },
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.pattern" } } }], size: 100, after: { value: '' } } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field2.elasticsearch_name}.keyword" } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: 100, after: { value: '' } } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.pattern" } } }], size: 100, after: { value: '' } } }
          }
        }
      end

      it 'searches using the correct body' do
        expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

        subject
      end

      context 'with from parameter' do
        let(:parameters) { { business_id: business.id, query: query, field_names: field_names, from: 100, kind: 'advanced' }.with_indifferent_access }
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { query_string: { query: query, analyzer: 'standard' } },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            track_total_hits: false, size: 0, from: 100,
            aggs: {
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: 100, after: { value: '' } } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.pattern" } } }], size: 100, after: { value: '' } } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: 100, after: { value: '' } } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.pattern" } } }], size: 100, after: { value: '' } } }
            }
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        it 'searches using the correct body' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end

        context 'when hits.length is equal to page size' do
          it 'returns next_page' do
            expect(subject[:next_page]).to eq 200
          end
        end

        context 'when hits.length is lower to page size' do
          let(:hits) { (1..5).map { |index| { a: index } } }

          it 'returns next_page' do
            expect(subject[:next_page]).to be_nil
          end
        end
      end

      context 'with stat parameter' do
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { multi_match: multi_match_param1 },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            track_total_hits: false, size: 0, from: 0,
            aggs: {
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field2.elasticsearch_name}.keyword" } }
            }
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        context 'when the stat exists' do
          let(:parameters) { { business_id: business.id, query: query, field_names: field_names, stat: 'uniqueness', kind: 'advanced' } }

          it 'searches using the correct body' do
            expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

            subject
          end
        end

        context 'when the stat does not exist' do
          let(:parameters) { { business_id: business.id, query: query, field_names: field_names, stat: 'foobar', kind: 'advanced' } }

          it 'raises a service error' do
            expect { subject }.to raise_error(Elasticsearch::BaseService::ServiceError, 'Atributo inválido')
          end
        end
      end

      context 'with pagination key parameter' do
        let(:parameters) { { business_id: business.id, query: query, field_names: field_names, kind: 'advanced', pagination_key: 'ABCFOOBAR' } }
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { multi_match: multi_match_param1 },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            track_total_hits: false, size: 0, from: 0,
            aggs: {
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field1.elasticsearch_name}.keyword" } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::OCCURRENCE_RESULTS, after: { value: parameters[:pagination_key] } } },
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.pattern" } } }], size: Elasticsearch::Constants::PATTERN_RESULTS, after: { value: parameters[:pagination_key] } } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::MISSING_STAT}" => { missing: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PRESENCE_STAT}" => { value_count: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::UNIQUENESS_STAT}" => { cardinality: { field: "#{@field2.elasticsearch_name}.keyword" } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::OCCURRENCE_COMPOSITE_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::OCCURRENCE_RESULTS, after: { value: parameters[:pagination_key] } } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::PATTERN_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.pattern" } } }], size: Elasticsearch::Constants::PATTERN_RESULTS, after: { value: parameters[:pagination_key] } } }
            }
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        it 'searches using the correct body' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end
      end
    end
  end

  describe '#find_similar_terms' do
    let(:business) { create(:business, :with_dependencies) }
    let(:term) { 'Francisco' }
    let(:field_names) { [@field1.label, @field2.label] }

    subject { described_class.new(parameters).find_similar_terms }

    before do
      @field1 = create(:field, :with_dependencies, label: 'field1', created_at: 2.days.ago)
      @field2 = create(:field, :with_dependencies, label: 'field2', created_at: 1.day.ago)

      create(:step_template, template: @field1.template, step: create(:step, business: business))
      create(:step_template, template: @field2.template, step: create(:step, business: business))

      allow(Elasticsearch::Client).to receive(:new).and_return(client)
    end

    context 'with simplified search' do
      let(:query) { 'Francesca' }
      let(:parameters) { { business_id: business.id, query: query, field_names: field_names, term: term } }
      let(:body) do
        {
          query: {
            bool: {
              must: [
                { match: { "#{@field2.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                { match: { "#{@field1.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                { multi_match: multi_match_param1 },
                bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
              ]
            }
          },
          _source: field_names, track_total_hits: false, size: 0, from: 0,
          aggs: {
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::SIMILAR_RESULTS, after: { value: '' } } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::SIMILAR_RESULTS, after: { value: '' } } }
          }
        }
      end

      it 'searches using the correct body' do
        expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

        subject
      end

      context 'with from parameter' do
        let(:parameters) { { business_id: business.id, query: query, field_names: field_names, from: 100, term: term } }
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { match: { "#{@field2.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                  { match: { "#{@field1.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                  { multi_match: multi_match_param1 },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            _source: field_names, track_total_hits: false, size: 0, from: 100,
            aggs: {
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::SIMILAR_RESULTS, after: { value: '' } } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::SIMILAR_RESULTS, after: { value: '' } } }
            }
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        it 'searches using the correct body' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end

        context 'when hits.length is equal to page size' do
          it 'returns next_page' do
            expect(subject[:next_page]).to eq 200
          end
        end

        context 'when hits.length is lower to page size' do
          let(:hits) { (1..5).map { |index| { a: index } } }

          it 'returns next_page' do
            expect(subject[:next_page]).to be_nil
          end
        end
      end

      context 'with pagination key parameter' do
        let(:parameters) { { business_id: business.id, query: query, field_names: field_names, term: term, pagination_key: 'ABCFOOBAR' } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { match: { "#{@field2.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                  { match: { "#{@field1.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                  { multi_match: multi_match_param1 },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            _source: field_names, track_total_hits: false, size: 0, from: 0,
            aggs: {
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::SIMILAR_RESULTS, after: { value: parameters[:pagination_key] } } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: Elasticsearch::Constants::SIMILAR_RESULTS, after: { value: parameters[:pagination_key] } } }
            }
          }
        end

        it 'searches using the correct body' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end
      end
    end

    context 'with advanced search' do
      let(:query) { "#{@field1.elasticsearch_name}:Franc*" }
      let(:parameters) { { business_id: business.id, query: query, field_names: field_names, term: term, kind: 'advanced' }.with_indifferent_access }
      let(:body) do
        {
          query: {
            bool: {
              must: [
                { match: { "#{@field2.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                { match: { "#{@field1.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                { query_string: { query: query, analyzer: 'standard' } },
                bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
              ]
            }
          },
          _source: field_names, track_total_hits: false, size: 0, from: 0,
          aggs: {
            "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: 100, after: { value: '' } } },
            "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: 100, after: { value: '' } } }
          }
        }
      end

      it 'searches using the correct body' do
        expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

        subject
      end

      context 'with from parameter' do
        let(:parameters) { { business_id: business.id, query: query, field_names: field_names, from: 100, term: term, kind: 'advanced' }.with_indifferent_access }
        let(:hits) { (1..Elasticsearch::Constants::PAGE_SIZE).map { |index| { a: index } } }
        let(:elastic_response) { { 'hits' => { 'hits' => hits } } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { match: { "#{@field2.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                  { match: { "#{@field1.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                  { query_string: { query: query, analyzer: 'standard' } },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            _source: field_names, track_total_hits: false, size: 0, from: 100,
            aggs: {
              "#{@field1.elasticsearch_name}_#{Elasticsearch::Constants::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: 100, after: { value: '' } } },
              "#{@field2.elasticsearch_name}_#{Elasticsearch::Constants::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: 100, after: { value: '' } } }
            }
          }
        end

        before { allow(client).to receive(:search).and_return elastic_response }

        it 'searches using the correct body' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end

        context 'when hits.length is equal to page size' do
          it 'returns next_page' do
            expect(subject[:next_page]).to eq 200
          end
        end

        context 'when hits.length is lower to page size' do
          let(:hits) { (1..5).map { |index| { a: index } } }

          it 'returns next_page' do
            expect(subject[:next_page]).to be_nil
          end
        end
      end

      context 'with pagination key parameter' do
        let(:parameters) { { business_id: business.id, query: query, field_names: field_names, term: term, pagination_key: 'ABCFOOBAR', kind: 'advanced' } }
        let(:body) do
          {
            query: {
              bool: {
                must: [
                  { match: { "#{@field2.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                  { match: { "#{@field1.elasticsearch_name}" => { query: term, fuzziness: 'AUTO:2,4', analyzer: 'text_analyzer' } } },
                  { multi_match: multi_match_param1 },
                  bool: { should: [{ match: { "#{Elasticsearch::Constants::BUSINESS_ID}" => business.id } }] }
                ]
              }
            },
            _source: field_names, track_total_hits: false, size: 0, from: 0,
            aggs: {
              "#{@field1.elasticsearch_name}_#{ElasticSearcherService::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field1.elasticsearch_name}.keyword" } } }], size: ElasticSearcherService::SIMILAR_RESULTS, after: { value: parameters[:pagination_key] } } },
              "#{@field2.elasticsearch_name}_#{ElasticSearcherService::SIMILARITY_STAT}" => { composite: { sources: [{ value: { terms: { field: "#{@field2.elasticsearch_name}.keyword" } } }], size: ElasticSearcherService::SIMILAR_RESULTS, after: { value: parameters[:pagination_key] } } }
            }
          }
        end

        it 'searches using the correct body' do
          expect(client).to receive(:search).with(index: "#{Apartment::Tenant.current}-*", body: body)

          subject
        end
      end
    end
  end
end
