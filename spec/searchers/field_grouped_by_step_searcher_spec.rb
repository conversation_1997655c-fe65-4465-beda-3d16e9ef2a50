require 'rails_helper'

RSpec.describe FieldGroupedByStepSearcher do
  before do
    @business1 = create(:business, :with_dependencies)
    @business2 = create(:business, :with_dependencies)
    @step1 = create(:step, business: @business1)
    @step2 = create(:step, business: @business2)
    @step3 = create(:step, business: @business2)
    @step4 = create(:step, business: @business1)

    @template1 = create(:template)
    create(:step_template, template: @template1, step: @step1, order: 0)
    @field1 = create(:field, label: 'field1', type: :text, template: @template1)
    @field2 = create(:field, :dropdown_type, label: 'field2', template: @template1)

    @template2 = create(:template)
    create(:step_template, template: @template2, step: @step4, order: 0)
    @field3 = create(:field, type: :date, label: 'field3', template: @template2)
    @field4 = create(:field, type: :telephone, label: 'field4', template: @template2)
  end

  context '#results' do
    let(:params) { { id: @business1.id } }
    subject { FieldGroupedByStepSearcher.new(params).results }

    it 'returns the fields with step' do
      is_expected.to eq([
                          { step_name: "#{@step1.business.name} - #{@step1.name}", have_parent_business: false, step_id: @step1.id, key_field_ids: [], fields: [@field1, @field2] },
                          { step_name: "#{@step4.business.name} - #{@step4.name}", have_parent_business: false, step_id: @step4.id, key_field_ids: [], fields: [@field3, @field4] }
                        ])
    end
    context 'with types param' do
      let(:params) { { id: @business1.id, types: %w[dropdown date] } }

      it 'returns the fields with step' do
        is_expected.to eq([
                            { step_name: "#{@step1.business.name} - #{@step1.name}", have_parent_business: false, key_field_ids: [], step_id: @step1.id, fields: [@field1, @field2] },
                            { step_name: "#{@step4.business.name} - #{@step4.name}", have_parent_business: false, key_field_ids: [], step_id: @step4.id, fields: [@field3, @field4] }
                          ])
      end
    end
    context 'with except_pk param' do
      let(:params) { { id: @business1.id, except_pk: true } }
      before { @business1.update(key_field_ids: [@field1.id]) }
      it 'returns the fields with step' do
        is_expected.to eq([
                            { step_name: "#{@step1.business.name} - #{@step1.name}", step_id: @step1.id, have_parent_business: false, key_field_ids: [@field1.id], fields: [@field2] },
                            { step_name: "#{@step4.business.name} - #{@step4.name}", step_id: @step4.id, have_parent_business: false, key_field_ids: [@field1.id], fields: [@field3, @field4] }
                          ])
      end
    end
    context 'with parent_business_fields' do
      let!(:subbusiness) { create(:business, :with_dependencies, :with_step, sub_business: true) }
      let!(:subbusiness_field) { create(:field, :with_dependencies, label: 'field1', type: :text) }
      let!(:parent_business) { create(:business, :with_dependencies, :with_step) }
      let!(:parent_business_field) { create(:field, :with_dependencies, type: :sub_business, reference_sub_business_id: subbusiness.id) }

      before do
        create(:step_template, step: parent_business.steps.first, template: parent_business_field.template)
        create(:step_template, step: subbusiness.steps.first, template: subbusiness_field.template)
      end

      let(:params) { { id: subbusiness.id, parent_business_fields: 'true' } }

      it do
        is_expected.to eq([
                            { step_name: "#{subbusiness.name} - #{subbusiness.steps.first.name}", have_parent_business: true, key_field_ids: [], step_id: subbusiness.steps.first.id, fields: [subbusiness_field] },
                            { step_name: "#{parent_business.name} - #{parent_business.steps.first.name}", have_parent_business: false, key_field_ids: [], step_id: parent_business.steps.first.id, fields: [parent_business_field] }
                          ])
      end
    end
    context 'with to_dependent_field_rule' do
      let!(:subbusiness) { create(:business, :with_dependencies, :with_step, sub_business: true) }
      let!(:subbusiness_field) { create(:field, :with_dependencies, label: 'field1', type: :text) }
      let!(:parent_business) { create(:business, :with_dependencies, :with_step) }
      let!(:parent_business_field) { create(:field, :with_dependencies, type: :sub_business, reference_sub_business_id: subbusiness.id) }
      let!(:parent_field) { create(:field, label: 'field1', type: :text, template: parent_business_field.template) }

      before do
        create(:step_template, step: parent_business.steps.first, template: parent_business_field.template)
        create(:step_template, step: subbusiness.steps.first, template: subbusiness_field.template)
      end

      let(:params) { { id: subbusiness.id, to_dependent_field_rule: 'true' } }

      it do
        is_expected.to eq([
                            { step_name: "#{subbusiness.name} - #{subbusiness.steps.first.name}", have_parent_business: true, key_field_ids: [], step_id: subbusiness.steps.first.id, fields: [subbusiness_field] },
                            { step_name: "#{parent_business.name} - #{parent_business.steps.first.name}", have_parent_business: false, key_field_ids: [], step_id: parent_business.steps.first.id, fields: [parent_business_field, parent_field] }
                          ])
      end
    end
  end
end
