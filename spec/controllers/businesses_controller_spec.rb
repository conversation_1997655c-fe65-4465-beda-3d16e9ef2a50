# == Schema Information
#
# Table name: businesses
#
#  id                                     :uuid             not null, primary key
#  name                                   :string           not null
#  description                            :string
#  deleted_at                             :datetime
#  business_group_id                      :uuid
#  created_at                             :datetime         not null
#  updated_at                             :datetime         not null
#  show_on_dashboard                      :boolean          default(FALSE)
#  sub_business                           :boolean          default(FALSE), not null
#  show_bulk_insert                       :boolean          default(FALSE), not null
#  show_bulk_alteration                   :boolean          default(FALSE), not null
#  bulk_insert_on_first_step_validates_pk :boolean          default(FALSE), not null
#

require 'rails_helper'

RSpec.describe BusinessesController, type: :controller do
  let(:business_group) { create(:business_group, name: 'ABA') }

  before do
    @admin = create(:administrator)
    @user = create(:user)

    @auth_headers = @admin.create_new_auth_token
    @user_auth_headers = @user.create_new_auth_token
  end

  describe 'GET index' do
    render_views

    let(:business_group2) { create(:business_group, name: 'BECA') }

    before do
      @business = create(:business, name: 'Foo', business_group: business_group)
      @business2 = create(:business, name: 'Car', deleted_at: Time.zone.now, business_group: business_group)
      @business3 = create(:business, name: 'Bar', business_group: business_group)

      @business4 = create(:business, name: 'Foo2', business_group: business_group2)
      @business5 = create(:business, name: 'Car2', deleted_at: Time.zone.now, business_group: business_group2)
      @business6 = create(:business, name: 'Bar2', business_group: business_group2)
    end
    context 'datatable request' do
      let(:datatable)  { double(:datatable, to_json: true) }
      let(:datatable_columns) { { 'columns' => { '0' => { 'data' => 'id', 'name' => '', 'searchable' => 'true', 'orderable' => 'true', 'search' => { 'value' => '', 'regex' => 'false' } } } } }
      let(:parameters) { @auth_headers.merge(datatable_columns) }

      it 'initializes the business datatable' do
        expect(BusinessDatatable).to receive(:new).and_return(datatable)

        get :index, xhr: true, format: :datatable, params: parameters
      end
    end

    context 'json request' do
      let(:parameters) { @auth_headers }

      it 'renders the index page' do
        get :index, xhr: true, format: :json, params: parameters

        expect(response).to render_template(:index)
      end

      it 'returns ok status' do
        get :index, xhr: true, format: :json, params: parameters

        expect(response).to have_http_status(:ok)
      end

      it 'renders the json structure' do
        get :index, xhr: true, format: :json, params: parameters

        expect_json_types('*', id: :string, name: :string, description: :string, deleted: :boolean, business_group_id: :string, business_group_name: :string)
      end

      it 'renders the businesses data ordered by business group, not deleted and name' do
        get :index, xhr: true, format: :json, params: parameters

        expect_json_sizes(6)

        expect_json('0', id: @business3.id, name: @business3.name, description: @business3.description, deleted: @business3.discarded?, business_group_id: @business3.business_group_id, business_group_name: @business3.business_group.name)
        expect_json('1', id: @business.id, name: @business.name, description: @business.description, deleted: @business.discarded?, business_group_id: @business.business_group_id, business_group_name: @business.business_group.name)
        expect_json('2', id: @business2.id, name: @business2.name, description: @business2.description, deleted: @business2.discarded?, business_group_id: @business2.business_group_id, business_group_name: @business2.business_group.name)
        expect_json('3', id: @business6.id, name: @business6.name, description: @business6.description, deleted: @business6.discarded?, business_group_id: @business6.business_group_id, business_group_name: @business6.business_group.name)
        expect_json('4', id: @business4.id, name: @business4.name, description: @business4.description, deleted: @business4.discarded?, business_group_id: @business4.business_group_id, business_group_name: @business4.business_group.name)
        expect_json('5', id: @business5.id, name: @business5.name, description: @business5.description, deleted: @business5.discarded?, business_group_id: @business5.business_group_id, business_group_name: @business5.business_group.name)
      end
    end
  end

  describe 'POST create' do
    context 'with invalid parameters' do
      let(:parameters) { { 'name' => '', 'description' => '', 'business_group_id' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        post :create, xhr: true, params: parameters

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the errors' do
        post :create, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'name' => 'Foo', 'description' => 'Bar', 'business_group_id' => business_group.id.to_s, 'sub_business' => 'true', 'show_on_list_created_at' => 'true', 'show_on_list_updated_at' => 'true', 'show_on_list_created_by_name' => 'true' } }

      it 'initializes the business service' do
        expect(BusinessService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          :sub_business,
          :name, :description, :type, :business_group_id, :show_on_dashboard, :show_on_top_answers, :show_bulk_alteration, :bulk_insert_on_first_step_validates_pk,
          :show_bulk_insert, :validate_fields, :enable_validation_web_service, :enable_verification_web_service, :fill_default_field_value, :validate_required_fields,
          :apply_field_rule, :apply_field_validation_rule, :notification, :help_url,
          :allow_to_skip_validations_when_bulking, :show_on_list_created_at, :show_on_list_updated_at,
          :show_on_list_created_by_name, :show_on_list_updated_by_name, :who_can_delete_contents, key_field_ids: []
          )).and_call_original

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'creates the business' do
        expect_any_instance_of(BusinessService).to receive(:create).and_call_original

        post :create, xhr: true, params: parameters.merge(@auth_headers)
      end

      it 'returns the created status' do
        post :create, xhr: true, params: parameters.merge(@auth_headers)

        expect(response).to have_http_status(:created)
      end
    end
  end

  describe 'GET export_all_steps_models' do
    before do
      @business = create(:business, business_group: business_group)
      @step = create(:step, business: @business)

      @template = create(:template)
      @step_template = create(:step_template, step: @step, template: @template)

      @field = create(:field, template: @template)
    end

    context 'when business not exists' do
      it 'returns not found status' do
        get :export_all_steps_models, xhr: true, params: { id: 0 }.merge(@auth_headers)

        expect(response).to have_http_status(:not_found)
      end
    end

    context 'when business exists' do
      it 'returns ok status' do
        get :export_all_steps_models, xhr: true, params: { id: @business.id }.merge(@auth_headers)

        expect(response).to have_http_status(:ok)
        expect(response.content_type).to eq('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        expect(response.body).not_to be_nil
      end
    end
  end

  describe 'PUT update' do
    before { @business = create(:business, business_group: business_group) }

    context 'with invalid parameters' do
      let(:parameters) {  { 'name' => '' }.merge(@auth_headers) }

      it 'returns the unprocessable entity status' do
        put :update, xhr: true, params: parameters.merge(id: @business.id)

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'returns the the errors' do
        put :update, xhr: true, params: parameters.merge(id: @business.id)

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with valid parameters' do
      let(:parameters) { { 'name' => 'Foo', 'description' => 'Bar', 'show_on_list_created_at' => 'true', 'show_on_list_updated_at' => 'false', 'show_on_list_created_by_name' => 'false' } }

      it 'initializes the business service' do
        expect(BusinessService).to receive(:new).with(ActionController::Parameters.new(parameters).permit(
          :name, :description, :type, :business_group_id, :show_on_dashboard, :show_on_top_answers, :show_bulk_alteration, :bulk_insert_on_first_step_validates_pk,
          :show_bulk_insert, :validate_fields, :enable_validation_web_service, :enable_verification_web_service, :fill_default_field_value, :validate_required_fields,
          :apply_field_rule, :apply_field_validation_rule, :notification, :help_url,
          :allow_to_skip_validations_when_bulking, :show_on_list_created_at, :show_on_list_updated_at,
          :show_on_list_created_by_name, :show_on_list_updated_by_name, :who_can_delete_contents, key_field_ids: []
        )).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @business.id).merge(@auth_headers)
      end

      it 'updates the business' do
        expect_any_instance_of(BusinessService).to receive(:update).with(@business.id.to_s).and_call_original

        put :update, xhr: true, params: parameters.merge(id: @business.id).merge(@auth_headers)
      end

      it 'returns the ok status' do
        put :update, xhr: true, params: parameters.merge(id: @business.id).merge(@auth_headers)

        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'GET show' do
    context 'when current user is not admin' do
      let(:parameters) { { id: @business.id }.merge(@user_auth_headers) }

      before do
        @business = create(:business, business_group: business_group)
        @step1 = create(:step, business: @business, order: 0)
        @step2 = create(:step, business: @business, order: 1, deleted_at: Time.zone.now)
        @step3 = create(:step, business: @business, order: 1)
        @template = create(:template)
        @step_template = create(:step_template, step: @step1, template: @template)
      end

      context 'when user is permitted' do
        before do
          create(:step_permission, step: @step1, user: @user)
        end

        it 'proceeds with the show action' do
          get :show, xhr: true, params: parameters

          expect(response).to have_http_status(:ok)
        end
      end

      context 'when user is not permitted' do
        before do
          StepPermission.where(step: @step1, user: @user).destroy_all
        end

        it 'renders a forbidden response' do
          get :show, xhr: true, params: parameters

          expect(response).to have_http_status(:forbidden)
          expect(response.body).to include(I18n.t('forbidden_to_view_record', scope: 'activerecord.errors.messages'))
        end
      end
    end

    context 'when current user is an admin' do
      render_views

      let(:parameters) { { id: @business.id }.merge(@auth_headers) }

      before do
        @business = create(:business, business_group: business_group)

        @step1 = create(:step, business: @business, order: 0)
        @step2 = create(:step, business: @business, order: 1, deleted_at: Time.zone.now)
        @step3 = create(:step, business: @business, order: 1)
        @step4 = create(:step, business: @business, order: 2)

        @template = create(:template)
        @step_template = create(:step_template, step: @step1, template: @template)
      end

      it 'renders the show page' do
        get :show, xhr: true, params: parameters

        expect(response).to render_template(:show)
      end

      it 'returns ok status' do
        get :show, xhr: true, params: parameters

        expect(response).to have_http_status(:ok)
      end

      it 'returns the json structure' do
        get :show, xhr: true, params: parameters

        expect_json_types(id: :string, name: :string, description: :string, deleted: :boolean, business_group_id: :string)
      end

      it 'returns the business data with the active steps' do
        get :show, xhr: true, params: parameters

        expect_json(id: @business.id, name: @business.name, description: @business.description, deleted: @business.discarded?, business_group_id: @business.business_group_id,
                    validate_fields: @business.validate_fields, fill_default_field_value: @business.fill_default_field_value, validate_required_fields: @business.validate_required_fields, apply_field_rule: @business.apply_field_rule,
                    apply_field_validation_rule: @business.apply_field_validation_rule, show_on_list_created_at: @business.show_on_list_created_at,
                    show_on_list_updated_at: @business.show_on_list_updated_at, show_on_list_created_by_name: @business.show_on_list_created_by_name)
      end
    end
  end

  describe 'DELETE destroy' do
    let(:parameters) { { id: @business.id }.merge(@auth_headers) }

    before do
      @business = create(:business, business_group: business_group)
    end

    context 'with error' do
      let(:business_service) { double(:business_service, destroy: false, success: false, errors: ['foo']) }

      before do
        allow(BusinessService).to receive(:new).and_return(business_service)
      end

      it 'returns bad request status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        delete :destroy, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the business service' do
        expect(BusinessService).to receive(:new).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'destroys the business' do
        expect_any_instance_of(BusinessService).to receive(:destroy).with(@business.id.to_s).and_call_original

        delete :destroy, xhr: true, params: parameters
      end

      it 'returns no content status' do
        delete :destroy, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end
  end

  describe 'PATCH activate' do
    let(:parameters) { { id: @business.id }.merge(@auth_headers) }

    before do
      @business = create(:business, deleted_at: Time.zone.now, business_group: business_group)
    end

    context 'with error' do
      let(:business_service) { double(:business_service, activate: false, success: false, errors: ['foo']) }

      before do
        allow(BusinessService).to receive(:new).and_return(business_service)
      end

      it 'returns bad request status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:bad_request)
      end

      it 'returns the the errors' do
        patch :activate, xhr: true, params: parameters

        expect_json_types(errors: :array_of_strings)
      end
    end

    context 'with success' do
      it 'initializes the business service' do
        expect(BusinessService).to receive(:new).and_call_original

        patch :activate, xhr: true, params: parameters
      end

      it 'activates the business' do
        expect_any_instance_of(BusinessService).to receive(:activate).with(@business.id.to_s).and_call_original

        patch :activate, xhr: true, params: parameters
      end

      it 'returns no content status' do
        patch :activate, xhr: true, params: parameters

        expect(response).to have_http_status(:no_content)
      end
    end
  end

  describe 'GET fields' do
    render_views

    context 'when business is the parent business' do
      let(:parameters) { { id: @business.id }.merge(@auth_headers) }

      before do
        @business = create(:business, business_group: business_group)
        @step = create(:step, business: @business)
        @template = create(:template)
        @step_template = create(:step_template, template: @template, step: @step, order: 0)
        @field1 = create(:field, :random_type, label: 'field1', template: @template)
        @field2 = create(:field, :random_type, label: 'field2', template: @template)
        @field3 = create(:field, :random_type, label: 'field3', template: @template)
        @field4 = create(:field, :random_type, label: 'field4', template: @template)
      end

      it 'renders the fields page' do
        get :fields, xhr: true, params: parameters

        expect(response).to render_template(:fields)
      end

      it 'returns ok status' do
        get :fields, xhr: true, params: parameters

        expect(response).to have_http_status(:ok)
      end

      context 'when field type is dropdown' do
        let(:option1) { { label: 'Option1', value: 'op1', order: 1 } }
        let(:option2) { { label: 'Option2', value: 'op2', order: 2 } }

        before do
          @UNPERMITTED_FIELD_TYPES_FOR_CONDITIONS = %w[upload link sub_business instruction].freeze
          @field5 = create(:field, type: Field.types[:dropdown], options: [option1, option2], label: 'field5', template: @template)
        end

        it 'returns the fields from the business' do
          get :fields, xhr: true, params: parameters

          expect_json([label: "#{@step.business.name} - #{@step.name}", have_parent_business: false, options: [
                        { label: @field1.label, value: "#{@step.id}:#{@field1.id}", order: 0, type: @field1.type, is_key: false, id: @field1.id, eligible_for_condition: !@UNPERMITTED_FIELD_TYPES_FOR_CONDITIONS.include?(@field1.type) },
                        { label: @field2.label, value: "#{@step.id}:#{@field2.id}", order: 1, type: @field2.type, is_key: false, id: @field2.id, eligible_for_condition: !@UNPERMITTED_FIELD_TYPES_FOR_CONDITIONS.include?(@field2.type) },
                        { label: @field3.label, value: "#{@step.id}:#{@field3.id}", order: 2, type: @field3.type, is_key: false, id: @field3.id, eligible_for_condition: !@UNPERMITTED_FIELD_TYPES_FOR_CONDITIONS.include?(@field3.type) },
                        { label: @field4.label, value: "#{@step.id}:#{@field4.id}", order: 3, type: @field4.type, is_key: false, id: @field4.id, eligible_for_condition: !@UNPERMITTED_FIELD_TYPES_FOR_CONDITIONS.include?(@field4.type) },
                        { label: @field5.label, value: "#{@step.id}:#{@field5.id}", options: [option1, option2], order: 4, type: @field5.type, is_key: false, id: @field5.id, eligible_for_condition: !@UNPERMITTED_FIELD_TYPES_FOR_CONDITIONS.include?(@field5.type) }
                      ]])
        end
      end
    end

    context 'when business is a subbusiness' do
      let(:parameters) { { id: @sub_business.id }.merge(@auth_headers) }

      before do
        @business = create(:business, :with_dependencies, :with_step)
        @sub_business = create(:business, :with_dependencies, sub_business: true)
        @sub_step = create(:step, business: @sub_business)
        @sub_template = create(:template)
        @sub_step_template = create(:step_template, template: @sub_template, step: @sub_step, order: 0)
        @sub_field_01 = create(:field, :with_dependencies, label: 'sub_field_01', order: 1, required: true, type: :sub_business, reference_sub_business_id: @sub_step.business.id, template: @sub_template)
      end

      it 'renders the fields page' do
        get :fields, xhr: true, params: parameters

        expect(response).to render_template(:fields)
      end

      it 'returns ok status' do
        get :fields, xhr: true, params: parameters

        expect(response).to have_http_status(:ok)
      end

      it 'returns the fields' do
        get :fields, xhr: true, params: parameters

        expect_json(
          [
            label: "#{@sub_step.business.name} - #{@sub_step.name}",
            have_parent_business: true,
            options: [
              { label: @sub_field_01.label, value: "#{@sub_step.id}:#{@sub_field_01.id}", order: 0, type: @sub_field_01.type, is_key: false, id: @sub_field_01.id, eligible_for_condition: false }
            ]
          ]
        )
      end
    end
  end
end
