image: atlassian/default-image:4

definitions:
  services:
    docker:
      memory: 3072

  steps:
    - step: &docker-build-and-push
        name: Docker Build and Push to ECR
        image: amazon/aws-cli:2.24.12
        cache:
          - docker
        services:
          - docker
        script:
          - chmod +x ./config/deploy/scripts/setup-aws-cli.sh
          - ./config/deploy/scripts/setup-aws-cli.sh
          - chmod +x ./config/deploy/scripts/docker-build.sh
          - ./config/deploy/scripts/docker-build.sh

    - step: &deploy-infra
        name: Deploy CloudFormation - infra
        image: amazon/aws-cli:2.24.12
        script:
          - chmod +x ./config/deploy/scripts/setup-aws-cli.sh
          - ./config/deploy/scripts/setup-aws-cli.sh
          - aws cloudformation deploy
            --template-file ./config/deploy/cloudformation/$BITBUCKET_DEPLOYMENT_ENVIRONMENT/cloud-formation-infra.yaml
            --stack-name fourmdg-$BITBUCKET_DEPLOYMENT_ENVIRONMENT-infra
            --tags Name=4mdg-$BITBUCKET_DEPLOYMENT_ENVIRONMENT
            --no-fail-on-empty-changeset
            --region $AWS_REGION
            --parameter-overrides MaxAsgSize=$MAX_ASG_SIZE
            --capabilities CAPABILITY_IAM CAPABILITY_NAMED_IAM


    - step: &generate-cloud-formation-backend-service
        name: Generate CloudFormation - backend-service
        script:
          - chmod +x config/deploy/scripts/cloud-formation-backend-service.sh
          - sh ./config/deploy/scripts/cloud-formation-backend-service.sh
        artifacts:
          - cloud-formation-backend-service.yml

    - step: &deploy-backend-stack
        name: Deploy CloudFormation - backend-service
        image: amazon/aws-cli:2.24.12
        cache:
          - docker
        services:
          - docker
        script:
          - chmod +x ./config/deploy/scripts/setup-aws-cli.sh
          - ./config/deploy/scripts/setup-aws-cli.sh
          - aws cloudformation deploy
            --template-file cloud-formation-backend-service.yml
            --stack-name fourmdg-$BITBUCKET_DEPLOYMENT_ENVIRONMENT-backend-service
            --tags Name=4mdg-$BITBUCKET_DEPLOYMENT_ENVIRONMENT
            --no-fail-on-empty-changeset
            --region $AWS_REGION
            --parameter-overrides MaxBackendServiceCapacity=$MAX_BACKEND_SERVICE_CAPACITY

    - step: &created-task-migrations
        name: Create ECS Task - Migrations
        image: amazon/aws-cli:2.24.12
        cache:
          - docker
        services:
          - docker
        script:
          - chmod +x ./config/deploy/scripts/setup-aws-cli.sh
          - ./config/deploy/scripts/setup-aws-cli.sh
          - |
            content=$(cat docker-compose.prod.cmd.yaml)
            content=${content//\$\{IMAGE_URL\}/"$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$BITBUCKET_REPO_SLUG:${BITBUCKET_COMMIT}-${BITBUCKET_DEPLOYMENT_ENVIRONMENT}"}
            content=${content//\$\{BRANCH\}/$BITBUCKET_DEPLOYMENT_ENVIRONMENT}
            echo "$content" > docker-compose.prod.cmd.yaml
          - curl https://amazon-ecs-cli.s3.amazonaws.com/ecs-cli-linux-amd64-latest -o ecs-cli
          - chmod +x ./ecs-cli
          - yum install -y sudo yum install -y https://s3.amazonaws.com/session-manager-downloads/plugin/latest/linux_64bit/session-manager-plugin.rpm
          - |
            ./ecs-cli compose --file ./docker-compose.prod.cmd.yaml  \
              --region $AWS_REGION \
              --ecs-params ./ecs-params.yml \
              --cluster $CLUSTER_NAME \
              --project-name $CLUSTER_NAME-migration create

    - step: &execution-migrations
        name: Run Database Migrations
        image: amazon/aws-cli:2.24.12
        cache:
          - docker
        services:
          - docker
        script:
          - chmod +x ./config/deploy/scripts/setup-aws-cli.sh
          - ./config/deploy/scripts/setup-aws-cli.sh
          - echo "Running migrations..."
          - |
            aws ecs run-task \
              --cluster $CLUSTER_NAME \
              --task-definition $CLUSTER_NAME-migration \
              --region $AWS_REGION \
              --overrides '{
                "containerOverrides": [{
                  "name": "rails_cmd",
                  "command": ["rails", "db:migrate", "db:seed", "--trace"]
                }]
              }'

    - step: &deploy-app
        name: Deploy - app
        script:
          - curl https://amazon-ecs-cli.s3.amazonaws.com/ecs-cli-linux-amd64-latest -o ecs-cli
          - chmod +x ./ecs-cli
          - |
            content=$(cat docker-compose.prod.yaml)
            content=${content//\$\{IMAGE_URL\}/"$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$BITBUCKET_REPO_SLUG:${BITBUCKET_COMMIT}-${BITBUCKET_DEPLOYMENT_ENVIRONMENT}"}
            content=${content//\$\{BRANCH\}/$BITBUCKET_DEPLOYMENT_ENVIRONMENT}
            echo "$content" > docker-compose.prod.yaml
          - |
            ./ecs-cli compose --file ./docker-compose.prod.yaml \
              --region $AWS_REGION \
              --ecs-params ./ecs-params.yml \
              --project-name $CLUSTER_NAME-backend-app service up \
              --timeout 15.00 \
              --deployment-max-percent 200 \
              --deployment-min-healthy-percent 50 \
              --cluster $CLUSTER_NAME \
              --force-deployment \
              --create-log-groups
          - |
            ./ecs-cli compose \
            --file ./docker-compose.prod.yaml \
            --region $AWS_REGION \
            --ecs-params ./ecs-params.yml \
            --project-name $CLUSTER_NAME-backend-app service scale \
            --cluster $CLUSTER_NAME $DESIRED_BACKEND_SERVICE_COUNT

    - step: &deploy-sidekiq
        name: Deploy - sidekiq
        script:
          - curl https://amazon-ecs-cli.s3.amazonaws.com/ecs-cli-linux-amd64-latest -o ecs-cli
          - chmod +x ./ecs-cli
          - |
            content=$(cat docker-compose.prod.sidekiq.yaml)
            content=${content//\$\{IMAGE_URL\}/"$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$BITBUCKET_REPO_SLUG:${BITBUCKET_COMMIT}-${BITBUCKET_DEPLOYMENT_ENVIRONMENT}"}
            content=${content//\$\{BRANCH\}/$BITBUCKET_DEPLOYMENT_ENVIRONMENT}
            echo "$content" > docker-compose.prod.sidekiq.yaml
          - |
            ./ecs-cli compose --file ./docker-compose.prod.sidekiq.yaml \
              --region $AWS_REGION \
              --ecs-params ./ecs-params.yml \
              --project-name $CLUSTER_NAME-sidekiq service up \
              --timeout 15.00 \
              --deployment-max-percent 200 \
              --deployment-min-healthy-percent 50 \
              --cluster $CLUSTER_NAME \
              --force-deployment \
              --create-log-groups
          - |
            ./ecs-cli compose --file ./docker-compose.prod.sidekiq.yaml \
              --region $AWS_REGION \
              --ecs-params ./ecs-params.yml \
              --project-name $CLUSTER_NAME-sidekiq service scale \
              --cluster $CLUSTER_NAME $DESIRED_SIDEKIQ_ELASTICSEARCH_SERVICE_COUNT

    - step: &deploy-sidekiq-elasticsearch
        name: Deploy - sidekiq-elasticsearch
        script:
          - curl https://amazon-ecs-cli.s3.amazonaws.com/ecs-cli-linux-amd64-latest -o ecs-cli
          - chmod +x ./ecs-cli
          - |
            content=$(cat docker-compose.prod.sidekiq-elasticsearch.yaml)
            content=${content//\$\{IMAGE_URL\}/"$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$BITBUCKET_REPO_SLUG:${BITBUCKET_COMMIT}-${BITBUCKET_DEPLOYMENT_ENVIRONMENT}"}
            content=${content//\$\{BRANCH\}/$BITBUCKET_DEPLOYMENT_ENVIRONMENT}
            echo "$content" > docker-compose.prod.sidekiq-elasticsearch.yaml
          - |
            ./ecs-cli compose --file ./docker-compose.prod.sidekiq-elasticsearch.yaml \
              --region $AWS_REGION \
              --ecs-params ./ecs-params.yml \
              --project-name $CLUSTER_NAME-sidekiq-elasticsearch service up \
              --timeout 15.00 \
              --deployment-max-percent 200 \
              --deployment-min-healthy-percent 50 \
              --cluster $CLUSTER_NAME \
              --force-deployment \
              --create-log-groups
          - |
            ./ecs-cli compose --file ./docker-compose.prod.sidekiq-elasticsearch.yaml \
              --region $AWS_REGION \
              --ecs-params ./ecs-params.yml \
              --project-name $CLUSTER_NAME-sidekiq-elasticsearch service scale \
              --cluster $CLUSTER_NAME $DESIRED_SIDEKIQ_ELASTICSEARCH_SERVICE_COUNT

    - step: &generate-cloud-formation-autoscale
        name: Generate CloudFormation - autoscale
        script:
          - chmod +x config/deploy/scripts/cloud-formation-autoscale.sh
          - bash ./config/deploy/scripts/cloud-formation-autoscale.sh
        artifacts:
          - cloud-formation-autoscale.yml

    - step: &deploy-autoscale
        name: Deploy CloudFormation - autoscale
        image: amazon/aws-cli:2.24.12
        cache:
          - docker
        services:
          - docker
        script:
          - chmod +x ./config/deploy/scripts/setup-aws-cli.sh
          - ./config/deploy/scripts/setup-aws-cli.sh
          - aws cloudformation deploy
            --template-file cloud-formation-autoscale.yml
            --stack-name fourmdg-$BITBUCKET_DEPLOYMENT_ENVIRONMENT-autoscale
            --tags Name=4mdg-$BITBUCKET_DEPLOYMENT_ENVIRONMENT
            --no-fail-on-empty-changeset
            --region $AWS_REGION

    - step: &run-test
        name: Run Tests
        size: 2x
        cache:
          - docker
        services:
          - docker
        script:
          - export DOCKER_BUILDKIT=0
          - docker-compose -f docker-compose.test.yaml up -d db redis
          - docker-compose -f docker-compose.test.yaml run -T -e RAILS_ENV=test web rails parallel:setup
          - docker-compose -f docker-compose.test.yaml run -T -e RAILS_ENV=test web rails parallel:spec

    - step: &run-brakeman
        name: Run brakeman
        cache:
          - docker
        services:
          - docker
        script:
          - export DOCKER_BUILDKIT=0
          - docker run -v "$(pwd)":/code presidentbeef/brakeman -z -o brakeman_results.html
        artifacts:
          - brakeman_results.html
    - step: &upload-brakeman-report
        name: Upload brakeman report
        image: amazon/aws-cli:2.24.12
        script:
          - chmod +x ./config/deploy/scripts/setup-aws-cli.sh
          - ./config/deploy/scripts/setup-aws-cli.sh
          - aws s3 cp ./brakeman_results.html
                s3://4mdg-brakeman/$BITBUCKET_DEPLOYMENT_ENVIRONMENT/brakeman_results.html
                --acl public-read


    - step: &generate-env
        name: Generate Env
        script:
          - chmod +x ./config/deploy/scripts/generate-env.sh
          - ./config/deploy/scripts/generate-env.sh
        artifacts:
          - .env.production

  stages:
    - stage: &build-and-deploy
        name: Build and deploy
        steps:
          - step: *docker-build-and-push
          - step: *deploy-infra
          - step: *generate-cloud-formation-backend-service
          - step: *deploy-backend-stack
          - step: *generate-env
          - step: *created-task-migrations
          - step: *execution-migrations
          - step: *deploy-app
          - step: *deploy-sidekiq
          - step: *deploy-sidekiq-elasticsearch
          - step: *generate-cloud-formation-autoscale
          - step: *deploy-autoscale

pipelines:
  custom:
    force-deploy-sandbox:
      - stage:
          name: Build and deploy
          deployment: sandbox
          steps:
            - step: *docker-build-and-push
            - step: *generate-env
            - step: *created-task-migrations
            - step: *execution-migrations
            - step: *deploy-app
            - step: *deploy-sidekiq
            - step: *deploy-sidekiq-elasticsearch

  pull-requests:
    "*/MDG-*":
      - stage:
          name: Pipeline test
          deployment: test
          steps:
            - step: *run-brakeman
            - step: *generate-env
            - step: *run-test
            - step: *upload-brakeman-report
    "release/*":
      - stage:
          name: Pipeline test
          deployment: test
          steps:
            - step: *run-brakeman
            - step: *generate-env
            - step: *run-test
            - step: *upload-brakeman-report

  branches:
    sandbox:
      - stage:
          <<: *build-and-deploy
          deployment: sandbox

    uat:
      - stage:
          <<: *build-and-deploy
          deployment: uat

    staging:
      - stage:
          <<: *build-and-deploy
          deployment: staging

    main:
      - stage:
          <<: *build-and-deploy
          deployment: master
