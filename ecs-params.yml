version: 1
task_definition:
  services:
    web:
      essential: true
      cpu_shares: 920
      mem_reservation: 760m
      mem_limit: 3072m
      healthcheck:
        test: ["CMD-SHELL", "curl -f -k http://localhost:3000/api/healthcheck || exit 1"]
    sidekiq:
      essential: true
      cpu_shares: 1024
      mem_reservation: 512m
      mem_limit: 1536m
      healthcheck:
        test: ["CMD-SHELL", "curl -f http://localhost:7433 || exit 1"]
    sidekiq-elasticsearch:
      essential: true
      cpu_shares: 1024
      mem_reservation: 512m
      mem_limit: 1536m
      healthcheck:
        test: ["CMD-SHELL", "curl -f http://localhost:7433 || exit 1"]
    rails_cmd:
      essential: true
      cpu_shares: 512
      mem_reservation: 380m
      mem_limit: 1512m
run_params:
  task_placement:
    strategy:
      - type: binpack
        field: memory
      - type: binpack
        field: cpu
      - type: spread
        field: instanceId
