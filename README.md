# README

## Instruções para o projeto

Ao fazer o clone do projeto, não se esqueça de instalar o overcommit na sua cópia local do repositório.

```
bundle exec overcommit --install
```

Isso faz com que os hooks do git sejam executados ao fazer o ```git pull```, antes de fazer o ```git commit``` e o ```git push```

## Alterar schema na sessão do psql ou pgadmin
```
  SET search_path TO cesar;
```

## Em desenvolvimento
* Para debug

```
  docker compose run --service-ports web
```

* <PERSON>riar o banco
```
docker compose run db -U postgres createdb fourmdg_development
```

* Gerar dump
```
  docker compose run web exe/fourmdg dump
```

* Restaurar dump
Mover o dump para a pasta backups
```
  docker compose up db
```

Em outro terminal:
```
  docker compose exec db sh
```

Dentro do shell do db:
```
  pg_restore -d fourmdg_development -h localhost -U postgres -v --no-owner < /var/lib/postgresql/backups/fourmdg.dump
```

* Rodar rakes

```
docker compose run web rails db:create db:migrate db:seed
```

* Se ocorrer o erro " A server is already running. Check /app/tmp/pids/server.pid."
```
docker compose run web rm -rf tmp
```

* Para acessar o letter_openner quando subir via docker: http://localhost:3000/letter_opener

* Para apagar as empresas sem schema no banco

```
  Company.find_each do |company|
    begin
      Apartment::Tenant.switch! company.subdomain
    rescue Apartment::TenantNotFound
      company.destroy!
    end
  end
```

* Remover um schema para criar novamente

```
BulkDestroyingContent.where(company: Company.find_by(subdomain: subdomain)).destroy_all
Company.find_by(subdomain: subdomain).destroy

CompanyService.new(
  name: subdomain,
  subdomain: subdomain,
  use_elasticsearch: false,
  expire_password_after_in_days: 90,
  enable_google_oauth: false,
  enable_microsoft_oauth: false,
  enable_internationalization: true,
  enable_email_and_password_login: true,
  enable_signup: false
).create

Apartment::Tenant.switch(subdomain) do
  Administrator.create!(name: 'TI Coyô', email: '<EMAIL>', password: 'C0y0@2014nyuk0')
  User.create!(name: 'Integração', email: '<EMAIL>', password: 'C0y0@2014nyuk0')
end
```

A senha precisa respeitar as seguintes regras:
> - Caracteres numéricos: mínimo de um número (1,2,3,4,5...)
> - Caracteres especiais: mínimo de um caractere especial (@$#.,)
> - Letras maiúsculas: mínimo de uma letra maiúscula (A,B,C,D,E...)|
> - Letras minúsculas: mínimo de uma letra minúscula (a,b,c,d, e...)
> - A senha não poderá ser igual às senhas anteriores

### AWS Elasticsearch

bundle exec sidekiq --index 1 --pidfile ./tmp/pids/sidekiq-1.pid  --environment development --queue elasticsearch --concurrency 1

* Executar o sidekiq para o elasticsearch
  ```
    bundle exec sidekiq --index 1 --pidfile ./tmp/pids/sidekiq-1.pid  --environment development --queue elasticsearch --concurrency 1
  ```

### Alterar a senha do usuário <EMAIL> nas bases
```
  Apartment::Tenant.switch! 'testes'

  Company.current.update(auth_domain: [], enable_google_oauth: false, enable_microsoft_oauth: false, enable_open_id: false)
  User.find_by(email: '<EMAIL>').tap { |user| user.assign_attributes(password: 'C0y0@2014nyuk0', provider: 'email', locked_at: nil, deleted_at: nil) }.save(validate: false)
  Administrator.find_by(email: '<EMAIL>').tap{ |administrator| administrator.assign_attributes(password: 'C0y0@2014nyuk0', provider: 'email') }.save(validate: false)
```

## Listar queries rodando a mais de 5 minutos
```
SELECT pid, now() - pg_stat_activity.query_start AS duration, query, state FROM pg_stat_activity WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';
```

### Listar queries com locks
```
SELECT pid, usename, pg_blocking_pids(pid) as blocked_by, query as blocked_query FROM pg_stat_activity WHERE cardinality(pg_blocking_pids(pid)) > 0;
```

### Encerrar queries com locks
```
SELECT pg_cancel_backend(a.pid), pg_terminate_backend(a.pid) FROM (SELECT pid, usename, pg_blocking_pids(pid) as blocked_by, query as blocked_query FROM pg_stat_activity WHERE cardinality(pg_blocking_pids(pid)) > 0) a;
```

### Remover jobs do sidekiq-alive que estão sem processar
```
queues = Sidekiq::Queue.all

queues.each do |queue|
  next unless queue.name.starts_with? 'sidekiq_alive-'

  registered_queues = SidekiqAlive.registered_instances.map { |i| "sidekiq_alive-#{i.split('::')[1]}" }

  next if registered_queues.include? queue.name

  puts "Clearing queue #{queue.name}"
  queue.clear
end
```

### Efetuar o vacuum do banco
```
SET maintenance_work_mem='4 GB';
\timing on
VACUUM FREEZE VERBOSE ANALYZE;
```

### Listar tamanho das bases
```
SELECT schema_name,
       pg_size_pretty(sum(table_size)::bigint) as absolute,
       (sum(table_size) / pg_database_size(current_database())) * 100 as relative
FROM (
  SELECT pg_catalog.pg_namespace.nspname as schema_name,
         pg_relation_size(pg_catalog.pg_class.oid) as table_size
  FROM   pg_catalog.pg_class
     JOIN pg_catalog.pg_namespace ON relnamespace = pg_catalog.pg_namespace.oid
) t
GROUP BY schema_name
ORDER BY sum(table_size)::bigint, schema_name;
```

### Listar tamanho das tabelas
```
SELECT table_schema, table_name, pg_size_pretty(pg_total_relation_size('"'||table_schema||'"."'||table_name||'"'))
FROM information_schema.tables
ORDER BY pg_total_relation_size('"'||table_schema||'"."'||table_name||'"') DESC;
```

### Rodar suíte de testes em paralelo
```
rake parallel:setup
rake parallel:spec
rake parallel:drop #dropar bases de dados criadas para os testes em paralelo
```

Mais informações: https://github.com/grosser/parallel_tests

### Listar as queries executadas
```
SELECT * FROM shared_extensions.pg_stat_statements;
```

https://www.timescale.com/blog/identify-postgresql-performance-bottlenecks-with-pg_stat_statements/
https://medium.com/squad-engineering/blazingly-fast-querying-on-huge-tables-by-avoiding-joins-5be0fca2f523
https://explain.depesz.com/

### Listar quantidade de contents não indexados no elasticsearch (por tenant/negócio)
```
not_found = {}

Company.order(:subdomain).pluck(:subdomain).each do |tenant|
  begin
    next unless Company.find_by(subdomain: tenant).try(:use_elasticsearch?)

    Apartment::Tenant.switch! tenant

    service = ElasticSearcherService.new

    Business.kept.find_each do |business|
      begin
        count = business.contents.kept.active.count

        next if count.zero?

        response = service.count_index(business.id)

        if count != response['count']
          not_found[tenant] ||= {}
          not_found[tenant][business.id] = count - response['count']
        end
      rescue Elastic::Transport::Transport::Errors::NotFound => e
        Rails.logger.info "Index #{tenant}-#{business.id} not found"
      end
    end
  rescue Apartment::TenantNotFound
    Rails.logger.error "[CheckNotUpdatedContentWorker][#{tenant}] Tenant not found"
  end
end

not_found
```

### Configurar o Janssen (GLUU)

Seguir o tutorial da documentação para instalação no Ubuntu: https://docs.jans.io/v1.0.14/admin/install/vm-install/ubuntu/

**Importante**
  * Criar a máquina com no mínimo 8GB de RAM (Large)
  * Durante a instalação do Janssen
    * Usar como HOST IP o endereço de IP privado da máquina
    * Criar uma entrada no DNS apontando para o IP público da máquina e usar essa entrada como FQDN
  * Após a instalação do Janssen, instalar o LetsEncrypt e gerar um certificado HTTPS para a máquina através do comando ```certbot --apache```


### Atualizar configurações dos índices do Elasticsearch sem reindexar todos os registros
```
client = ElasticSearcherService.new.send(:client)
response = client.indices.put_settings(
  index: 'uberlandiarefrescos-e9202b9a-76b9-486b-b7c4-88475a58ef51',
  body: {
    index: {
      "mapping.total_fields.limit": Elasticsearch::Constants::MAPPING_TOTAL_FIELDS_LIMIT
    }
  }
)
```
Alterar o corpo da requisição conforme necessidade.
