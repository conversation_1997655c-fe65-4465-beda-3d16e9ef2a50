services:
  web:
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: .docker/dev/entrypoint_web.sh
    # If you need to run the server using a self signed certificate (to serve HTTPS requests), uncomment the line below
    # command: /bin/sh -c "rm -f /app/tmp/pids/server.pid && bundle exec rails s -p 3000 -b 'ssl://0.0.0.0?key=keys/localhost.key&cert=keys/localhost.crt'"
    volumes:
      - .:/app
    ports:
      - "3000:3000"
    depends_on:
      - db
      - redis
    stdin_open: true
    tty: true

  db:
    image: "postgres:17-alpine"
    environment:
      - POSTGRES_HOST=db
      - POSTGRES_USERNAME=postgres
      - POSTGRES_PASSWORD=93pbf34K8p7A
    ports:
      - "5433:5432"
    volumes:
      - "postgres:/var/lib/postgresql/data"
      - "./backups:/var/lib/postgresql/backups"

  sidekiq:
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: bundle exec sidekiq
    # uncomment the next line for workers debug
    # command: tail -f /dev/null
    volumes:
      - .:/app
    depends_on:
      - db
      - redis

  sidekiq-elasticsearch:
    build:
      context: .
      dockerfile: Dockerfile.dev
    command: bundle exec sidekiq -C config/sidekiq-elasticsearch.yml
    volumes:
      - .:/app
    depends_on:
      - db
      - redis

  redis:
    image: redis:7-alpine
    volumes:
      - "redis:/var/lib/redis/data"
    ports:
      - "6380:6379"

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:${STACK_VERSION}
    deploy:
      resources:
        limits:
          memory: 2G
    env_file: .env
    labels:
      co.elastic.logs/module: elasticsearch
    volumes:
      - esdata01:/usr/share/elasticsearch/data
    ports:
      - 9200:9200
    environment:
      - node.name=elasticsearch
      - discovery.type=single-node
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD}
      - bootstrap.memory_lock=true
      - xpack.security.enabled=false
      - xpack.license.self_generated.type=basic
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "curl -s http://localhost:9200 | grep -q 'You Know, for Search'",
        ]
      interval: 10s
      timeout: 10s
      retries: 120

  kibana:
    depends_on:
      elasticsearch:
        condition: service_healthy
    image: docker.elastic.co/kibana/kibana:${STACK_VERSION}
    env_file: .env
    labels:
      co.elastic.logs/module: kibana
    volumes:
      - kibanadata:/usr/share/kibana/data
    ports:
      - 5601:5601
    environment:
      - SERVERNAME=kibana
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=${KIBANA_PASSWORD}
      - XPACK_SECURITY_ENCRYPTIONKEY=${ENCRYPTION_KEY}
      - XPACK_ENCRYPTEDSAVEDOBJECTS_ENCRYPTIONKEY=${ENCRYPTION_KEY}
      - XPACK_REPORTING_ENCRYPTIONKEY=${ENCRYPTION_KEY}
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "curl -s -I http://localhost:5601 | grep -q 'HTTP/1.1 302 Found'",
        ]
      interval: 10s
      timeout: 10s
      retries: 120

volumes:
  esdata01:
  kibanadata:
  redis:
  postgres:

networks:
  default:
    name: fourmdg-net
    driver: bridge
    external: false
