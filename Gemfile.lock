GEM
  remote: https://rubygems.org/
  specs:
    aasm (5.5.0)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activemodel-serializers-xml (1.0.3)
      activemodel (>= 5.0.0.a)
      activesupport (>= 5.0.0.a)
      builder (~> 3.1)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      mutex_m
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    aes_key_wrap (1.1.0)
    airborne (0.3.7)
      activesupport
      rack
      rack-test (>= 1.1.0, < 2.0)
      rest-client (>= 2.0.2, < 3.0)
      rspec (~> 3.8)
    ajax-datatables-rails (1.1.0)
      railties (>= 4.2)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    appsignal (4.1.2)
      logger
      rack
    ast (2.4.2)
    async (2.17.0)
      console (~> 1.26)
      fiber-annotation
      io-event (~> 1.6, >= 1.6.5)
    async-container (0.18.3)
      async (~> 2.10)
    async-http (0.82.2)
      async (>= 2.10.2)
      async-pool (~> 0.9)
      io-endpoint (~> 0.14)
      io-stream (~> 0.6)
      metrics (~> 0.12)
      protocol-http (~> 0.37)
      protocol-http1 (>= 0.28.1)
      protocol-http2 (~> 0.19)
      traces (~> 0.10)
    async-http-cache (0.4.4)
      async-http (~> 0.56)
    async-pool (0.10.1)
      async (>= 1.25)
      traces
    async-service (0.12.0)
      async
      async-container (~> 0.16)
    attr_required (1.0.2)
    aws-eventstream (1.3.0)
    aws-partitions (1.995.0)
    aws-sdk-cloudwatch (1.104.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.211.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.95.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.169.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-wafv2 (1.97.0)
      aws-sdk-core (~> 3, >= 3.210.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.10.1)
      aws-eventstream (~> 1, >= 1.0.2)
    axiom-types (0.1.1)
      descendants_tracker (~> 0.0.4)
      ice_nine (~> 0.11.0)
      thread_safe (~> 0.3, >= 0.3.1)
    base64 (0.2.0)
    bcrypt (3.1.20)
    benchmark-memory (0.2.0)
      memory_profiler (~> 1)
    bigdecimal (3.1.8)
    bindata (2.5.0)
    brakeman (7.0.0)
      racc
    builder (3.3.0)
    bullet (7.2.0)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    bundler-audit (0.9.2)
      bundler (>= 1.2.0, < 3)
      thor (~> 1.0)
    byebug (11.1.3)
    childprocess (5.1.0)
      logger (~> 1.5)
    cmath (1.0.0)
    code_analyzer (0.5.5)
      sexp_processor
    coercible (1.0.0)
      descendants_tracker (~> 0.0.1)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    console (1.27.0)
      fiber-annotation
      fiber-local (~> 1.1)
      json
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    date (3.3.4)
    descendants_tracker (0.0.4)
      thread_safe (~> 0.3, >= 0.3.1)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-security (0.15.0)
      devise (>= 4.3.0, < 5.0)
    devise_token_auth (1.2.3)
      bcrypt (~> 3.0)
      devise (> 3.5.2, < 5)
      rails (>= 4.2.0, < 7.2)
    diff-lcs (1.5.1)
    discard (1.3.0)
      activerecord (>= 4.2, < 8)
    docile (1.4.1)
    domain_name (0.6.20240107)
    draper (4.0.2)
      actionpack (>= 5.0)
      activemodel (>= 5.0)
      activemodel-serializers-xml (>= 1.0)
      activesupport (>= 5.0)
      request_store (>= 1.0)
      ruby2_keywords
    drb (2.2.1)
    elastic-transport (8.4.0)
      faraday (< 3)
      multi_json
    elasticsearch (9.0.3)
      elastic-transport (~> 8.3)
      elasticsearch-api (= 9.0.3)
    elasticsearch-api (9.0.3)
      multi_json
    erubi (1.13.0)
    erubis (2.7.0)
    et-orbi (1.2.11)
      tzinfo
    excon (0.112.0)
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    falcon (0.48.3)
      async
      async-container (~> 0.18)
      async-http (~> 0.75)
      async-http-cache (~> 0.4)
      async-service (~> 0.10)
      bundler
      localhost (~> 1.1)
      openssl (~> 3.0)
      process-metrics (~> 0.2)
      protocol-http (~> 0.31)
      protocol-rack (~> 0.7)
      samovar (~> 2.3)
    faraday (1.10.4)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (1.0.2)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    faraday_middleware (1.2.1)
      faraday (~> 1.0)
    fasterer (0.11.0)
      ruby_parser (>= 3.19.1)
    ffi (1.16.3)
    fiber-annotation (0.2.0)
    fiber-local (1.1.0)
      fiber-storage
    fiber-storage (1.0.0)
    flay (2.13.3)
      erubi (~> 1.10)
      path_expander (~> 1.0)
      ruby_parser (~> 3.0)
      sexp_processor (~> 4.0)
    flog (4.8.0)
      path_expander (~> 1.0)
      ruby_parser (~> 3.1, > 3.1.0)
      sexp_processor (~> 4.8)
    fog-aws (3.28.0)
      base64 (~> 0.2.0)
      fog-core (~> 2.1)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
    fog-core (2.5.0)
      builder
      excon (~> 0.71)
      formatador (>= 0.2, < 2.0)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.4)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (1.1.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    fuubar (2.5.1)
      rspec-core (~> 3.0)
      ruby-progressbar (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    gserver (0.0.1)
    hashdiff (1.1.1)
    hashie (5.0.0)
    http-accept (1.7.0)
    http-cookie (1.0.7)
      domain_name (~> 0.5)
    httpclient (2.8.3)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    i18n_generators (2.2.2)
      activerecord (>= 3.0.0)
      railties (>= 3.0.0)
    ice_nine (0.11.2)
    iniparse (1.5.0)
    io-console (0.7.2)
    io-endpoint (0.14.0)
    io-event (1.7.3)
    io-stream (0.6.0)
    irb (1.14.1)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    json (2.7.3)
    json-jwt (********)
      activesupport (>= 4.2)
      aes_key_wrap
      bindata
      httpclient
    json-schema (5.2.1)
      addressable (~> 2.8)
      bigdecimal (~> 3.1)
    jwt (2.9.3)
      base64
    keisan (0.9.2)
      cmath (~> 1.0)
    kwalify (0.7.2)
    language_server-protocol (********)
    launchy (3.0.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    letter_opener_web (1.4.1)
      actionmailer (>= 3.2)
      letter_opener (~> 1.0)
      railties (>= 3.2)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    localhost (1.3.1)
    logger (1.6.1)
    loofah (2.23.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    mapping (1.1.1)
    marcel (1.0.4)
    memory_profiler (1.1.0)
    metrics (0.12.0)
    mime-types (3.6.0)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.1001)
    mini_mime (1.1.5)
    minitest (5.25.1)
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mutex_m (0.2.0)
    net-imap (0.5.0)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-smtp (0.5.0)
      net-protocol
    net-ssh (7.3.0)
    netaddr (2.0.6)
    netrc (0.11.0)
    nio4r (2.7.3)
    nokogiri (1.16.7-x86_64-linux)
      racc (~> 1.4)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    oj (3.16.6)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    omniauth (2.1.2)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-entra-id (3.0.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-google-oauth2 (1.2.0)
      jwt (>= 2.9)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth_openid_connect (0.6.1)
      omniauth (>= 1.9, < 3)
      openid_connect (~> 1.1)
    openid_connect (1.4.2)
      activemodel
      attr_required (>= 1.0.0)
      json-jwt (>= 1.15.0)
      net-smtp
      rack-oauth2 (~> 1.21)
      swd (~> 1.3)
      tzinfo
      validate_email
      validate_url
      webfinger (~> 1.2)
    openssl (3.2.0)
    orm_adapter (0.5.0)
    ostruct (0.6.0)
    overcommit (0.64.0)
      childprocess (>= 0.6.3, < 6)
      iniparse (~> 1.4)
      rexml (~> 3.2)
    paper_trail (15.2.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.26.3)
    parallel_tests (4.7.2)
      parallel
    parser (*******)
      ast (~> 2.4.1)
      racc
    path_expander (1.1.3)
    pg (1.5.8)
    pg_search (2.3.7)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    process-metrics (0.3.0)
      console (~> 1.8)
      json (~> 2)
      samovar (~> 2.1)
    protocol-hpack (1.5.1)
    protocol-http (0.42.0)
    protocol-http1 (0.28.1)
      protocol-http (~> 0.22)
    protocol-http2 (0.19.3)
      protocol-hpack (~> 1.4)
      protocol-http (~> 0.18)
    protocol-rack (0.10.1)
      protocol-http (~> 0.37)
      rack (>= 1.0)
    psych (5.1.2)
      stringio
    public_suffix (5.1.1)
    pundit (2.5.0)
      activesupport (>= 3.0.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.10)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-oauth2 (1.21.3)
      activesupport
      attr_required
      httpclient
      json-jwt (>= 1.11.0)
      rack (>= 2.1.0)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-robustness (1.2.0)
    rack-session (1.0.2)
      rack (< 3)
    rack-test (1.1.0)
      rack (>= 1.0, < 3)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    rails-pg-extras (5.4.1)
      rails
      ruby-pg-extras (= 5.4.1)
    rails_best_practices (1.23.2)
      activesupport
      code_analyzer (~> 0.5.5)
      erubis
      i18n
      json
      require_all (~> 3.0)
      ruby-progressbar
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rdoc (6.7.0)
      psych (>= 4.0.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.25.1)
      connection_pool
    reek (6.1.4)
      kwalify (~> 0.7.0)
      parser (~> 3.2.0)
      rainbow (>= 2.0, < 4.0)
    regexp_parser (2.9.2)
    reline (0.5.10)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    require_all (3.0.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.3.9)
    rollbar (3.6.0)
    ros-apartment (3.1.0)
      activerecord (>= 6.1.0, < 7.2)
      parallel (< 2.0)
      public_suffix (>= 2.0.5, < 6.0)
      rack (>= 1.3.6, < 4.0)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-activemodel-mocks (1.2.1)
      activemodel (>= 3.0)
      activesupport (>= 3.0)
      rspec-mocks (>= 2.99, < 4.0)
    rspec-collection_matchers (1.2.1)
      rspec-expectations (>= 2.99.0.beta1)
    rspec-core (3.13.2)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.0.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.1)
    rubocop (1.59.0)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.30.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.30.0)
      parser (>= *******)
    rubocop-rails (2.23.1)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.33.0, < 2.0)
      rubocop-ast (>= 1.30.0, < 2.0)
    ruby-pg-extras (5.4.1)
      pg
      terminal-table
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    rubyXL (3.4.27)
      nokogiri (>= 1.10.8)
      rubyzip (>= 1.3.0)
    ruby_parser (3.21.1)
      racc (~> 1.5)
      sexp_processor (~> 4.16)
    rubycritic (4.9.1)
      flay (~> 2.13)
      flog (~> 4.7)
      launchy (>= 2.5.2)
      parser (>= *******)
      rainbow (~> 3.1.1)
      reek (~> 6.0, < 6.2)
      rexml
      ruby_parser (~> 3.21)
      simplecov (>= 0.22.0)
      tty-which (~> 0.5.0)
      virtus (~> 2.0)
    rubyzip (2.3.2)
    rufus-scheduler (3.9.2)
      fugit (~> 1.1, >= 1.11.1)
    samovar (2.3.0)
      console (~> 1.0)
      mapping (~> 1.0)
    sentry-rails (5.22.1)
      railties (>= 5.0)
      sentry-ruby (~> 5.22.1)
    sentry-ruby (5.22.1)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    sexp_processor (4.17.2)
    should_not (1.1.0)
    shoulda-matchers (6.4.0)
      activesupport (>= 5.2.0)
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-bulk (0.2.0)
      sidekiq
    sidekiq-scheduler (5.0.6)
      rufus-scheduler (~> 3.2)
      sidekiq (>= 6, < 8)
      tilt (>= 1.4.0, < 3)
    sidekiq-unique-jobs (8.0.10)
      concurrent-ruby (~> 1.0, >= 1.0.5)
      sidekiq (>= 7.0.0, < 8.0.0)
      thor (>= 1.0, < 3.0)
    sidekiq_alive (2.4.0)
      gserver (~> 0.0.1)
      sidekiq (>= 5, < 8)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    spring (3.1.1)
    stringio (3.1.1)
    swd (1.3.0)
      activesupport (>= 3)
      attr_required (>= 0.0.5)
      httpclient (>= 2.4)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    thor (1.3.2)
    thread_safe (0.3.6)
    tilt (2.4.0)
    timecop (0.9.10)
    timeout (0.4.1)
    to_regexp (0.2.1)
    traces (0.13.1)
    translate_enum (0.2.0)
      activesupport
    tty-which (0.5.0)
    twilio-ruby (7.3.5)
      faraday (>= 0.9, < 3.0)
      jwt (>= 1.5, < 3.0)
      nokogiri (>= 1.6, < 2.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2024.2)
      tzinfo (>= 1.0.0)
    unicode-display_width (2.6.0)
    uniform_notifier (1.16.0)
    validate_email (0.1.6)
      activemodel (>= 3.0)
      mail (>= 2.2.5)
    validate_url (1.0.15)
      activemodel (>= 3.0.0)
      public_suffix
    version_gem (1.1.4)
    virtus (2.0.0)
      axiom-types (~> 0.1)
      coercible (~> 1.0)
      descendants_tracker (~> 0.0, >= 0.0.3)
    warden (1.2.9)
      rack (>= 2.0.9)
    webfinger (1.2.0)
      activesupport
      httpclient (>= 2.4)
    webmock (3.24.0)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.8.2)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    will_paginate (4.0.1)
    zeitwerk (2.7.1)

PLATFORMS
  x86_64-linux
  x86_64-linux-musl

DEPENDENCIES
  aasm
  airborne
  ajax-datatables-rails
  annotate
  appsignal
  aws-sdk-cloudwatch
  aws-sdk-s3
  aws-sdk-wafv2
  benchmark-memory
  brakeman
  bullet
  bundler-audit
  byebug
  devise
  devise-security
  devise_token_auth
  discard
  draper
  elasticsearch
  factory_bot_rails
  faker
  falcon
  faraday_middleware
  fasterer
  ffi
  fog-aws
  fuubar
  i18n_generators
  jbuilder
  json-schema
  keisan
  letter_opener
  letter_opener_web
  listen
  memory_profiler
  multi_json
  net-scp
  net-ssh
  netaddr
  oj
  omniauth
  omniauth-entra-id
  omniauth-google-oauth2
  omniauth_openid_connect
  overcommit
  paper_trail
  parallel_tests
  pg
  pg_search
  psych
  pundit
  rack-cors
  rack-robustness
  rails
  rails-controller-testing
  rails-pg-extras
  rails_best_practices
  redis
  rest-client
  rollbar
  ros-apartment
  rspec-activemodel-mocks
  rspec-collection_matchers
  rspec-rails
  rubocop
  rubocop-rails
  ruby-progressbar
  rubyXL
  rubycritic
  sentry-rails
  sentry-ruby
  should_not
  shoulda-matchers
  sidekiq
  sidekiq-bulk
  sidekiq-scheduler
  sidekiq-unique-jobs
  sidekiq_alive
  simplecov
  spring
  thor
  timecop
  to_regexp
  translate_enum
  twilio-ruby
  tzinfo-data
  validate_url
  webmock
  webrick
  will_paginate

RUBY VERSION
   ruby 3.2.5p208

BUNDLED WITH
   2.6.9
