# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_07_22_033346) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_trgm"
  enable_extension "pgcrypto"
  enable_extension "plpgsql"
  enable_extension "unaccent"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.uuid "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.bigint "byte_size", null: false
    t.string "checksum", null: false
    t.datetime "created_at", precision: nil, null: false
    t.string "service_name", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "administrators", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "provider", default: "email", null: false
    t.string "uid", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "unconfirmed_email"
    t.string "name"
    t.string "nickname"
    t.string "image"
    t.string "email"
    t.jsonb "tokens"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "allow_password_change", default: false, null: false
    t.boolean "approved", default: true, null: false
    t.datetime "deleted_at", precision: nil
    t.boolean "owner", default: false, null: false
    t.datetime "last_active_at", precision: nil
    t.string "authorization_token"
    t.index ["confirmation_token"], name: "index_administrators_on_confirmation_token", unique: true
    t.index ["email"], name: "index_administrators_on_email", unique: true
    t.index ["reset_password_token"], name: "index_administrators_on_reset_password_token", unique: true
    t.index ["uid", "provider"], name: "index_administrators_on_uid_and_provider", unique: true
  end

  create_table "alteration_processings", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "bulk_saving_answer_id"
    t.integer "status", default: 0, null: false
    t.integer "bulk_action", null: false
    t.jsonb "criterions", null: false
    t.jsonb "alterations"
    t.jsonb "approvals"
    t.integer "successes", default: 0
    t.jsonb "processing_errors", default: []
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "total_alterations"
    t.integer "processed_count", default: 0, null: false
    t.string "validation_url"
    t.string "verification_url"
    t.index ["bulk_saving_answer_id"], name: "index_alteration_processings_on_bulk_saving_answer_id"
  end

  create_table "answer_processings", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "answer_id"
    t.uuid "bulk_saving_answer_id"
    t.integer "status", default: 0, null: false
    t.jsonb "data"
    t.string "processing_errors"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.jsonb "data_with_verification_url_response"
    t.index ["answer_id"], name: "index_answer_processings_on_answer_id"
    t.index ["bulk_saving_answer_id"], name: "index_answer_processings_on_bulk_saving_answer_id"
  end

  create_table "answer_versions", force: :cascade do |t|
    t.uuid "whodunnit_id"
    t.uuid "answer_id"
    t.jsonb "values"
    t.jsonb "form_values"
    t.jsonb "object_changes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "event", default: 0, null: false
    t.inet "ip"
    t.datetime "filled_at", precision: nil
    t.uuid "step_authorizer_id"
    t.integer "origin"
    t.integer "old_status"
    t.integer "new_status"
    t.text "event_comment"
    t.index ["answer_id"], name: "index_answer_versions_on_answer_id"
  end

  create_table "answers", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.jsonb "data"
    t.uuid "content_id"
    t.uuid "step_id"
    t.uuid "user_id"
    t.datetime "deleted_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "position", default: 0, null: false
    t.integer "status", default: 0, null: false
    t.datetime "concluded_at", precision: nil
    t.datetime "authorized_at", precision: nil
    t.uuid "authorizer_id"
    t.datetime "filled_at", precision: nil
    t.datetime "available_at", precision: nil
    t.boolean "required_authorization"
    t.uuid "created_by_id"
    t.datetime "first_fill_at", precision: nil
    t.uuid "review_requested_by_id"
    t.inet "last_update_ip"
    t.uuid "step_authorizer_id"
    t.string "authorizer_token"
    t.integer "origin"
    t.index ["content_id"], name: "index_answers_on_content_id"
    t.index ["status"], name: "index_answers_on_status"
    t.index ["step_id"], name: "index_answers_on_step_id"
    t.index ["updated_at"], name: "index_answers_on_updated_at"
    t.index ["user_id"], name: "index_answers_on_user_id"
  end

  create_table "bulk_destroying_contents", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.datetime "start_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }
    t.datetime "end_at", precision: nil
    t.uuid "company_id"
    t.string "business_id"
    t.string "business_name"
    t.integer "status", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_bulk_destroying_contents_on_company_id"
  end

  create_table "bulk_saving_answers", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.datetime "start_at", precision: nil, default: -> { "CURRENT_TIMESTAMP" }
    t.datetime "end_at", precision: nil
    t.integer "origin", default: 0, null: false
    t.uuid "step_id"
    t.uuid "user_id"
    t.integer "status", default: 0, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.uuid "business_id"
    t.boolean "enable_verification_url", default: true, null: false
    t.boolean "enable_validation_url", default: true, null: false
    t.boolean "enable_field_validations", default: true, null: false
    t.boolean "enable_business_validations", default: true, null: false
    t.inet "ip"
    t.index ["business_id"], name: "index_bulk_saving_answers_on_business_id"
    t.index ["step_id"], name: "index_bulk_saving_answers_on_step_id"
    t.index ["user_id"], name: "index_bulk_saving_answers_on_user_id"
  end

  create_table "business_groups", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.string "icon"
    t.index ["deleted_at"], name: "index_business_groups_on_deleted_at"
    t.index ["name"], name: "index_business_groups_on_name"
  end

  create_table "business_headers", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "business_id", null: false
    t.uuid "field_id", null: false
    t.uuid "step_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["business_id"], name: "index_business_headers_on_business_id"
    t.index ["field_id"], name: "index_business_headers_on_field_id"
    t.index ["step_id"], name: "index_business_headers_on_step_id"
  end

  create_table "businesses", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "name", null: false
    t.string "description"
    t.datetime "deleted_at", precision: nil
    t.uuid "business_group_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "show_on_dashboard", default: false
    t.boolean "sub_business", default: false, null: false
    t.boolean "show_bulk_insert", default: false, null: false
    t.boolean "show_bulk_alteration", default: false, null: false
    t.boolean "bulk_insert_on_first_step_validates_pk", default: false, null: false
    t.boolean "validate_fields", default: true, null: false
    t.boolean "fill_default_field_value", default: true, null: false
    t.boolean "validate_required_fields", default: true, null: false
    t.boolean "apply_field_rule", default: true, null: false
    t.boolean "apply_field_validation_rule", default: true, null: false
    t.boolean "notification", default: false, null: false
    t.string "help_url"
    t.boolean "show_on_top_answers", default: false, null: false
    t.boolean "show_on_list_created_at", default: false
    t.boolean "show_on_list_updated_at", default: false
    t.boolean "show_on_list_created_by_name", default: false
    t.boolean "enable_validation_web_service", default: true
    t.boolean "allow_to_skip_validations_when_bulking", default: false, null: false
    t.boolean "enable_verification_web_service", default: true
    t.boolean "show_on_list_updated_by_name", default: false
    t.integer "who_can_delete_contents", default: 0, null: false
    t.string "webhook_url"
    t.string "icon"
    t.boolean "integrate_elastic", default: false, null: false
    t.boolean "skip_webhook", default: false
    t.index ["business_group_id"], name: "index_businesses_on_business_group_id"
    t.index ["deleted_at"], name: "index_businesses_on_deleted_at"
    t.index ["name"], name: "index_businesses_on_name"
    t.index ["sub_business"], name: "index_businesses_on_sub_business"
  end

  create_table "businesses_fields", id: false, force: :cascade do |t|
    t.uuid "business_id"
    t.uuid "field_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["business_id"], name: "index_businesses_fields_on_business_id"
    t.index ["field_id"], name: "index_businesses_fields_on_field_id"
  end

  create_table "companies", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "name", null: false
    t.string "subdomain", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "api_key"
    t.bigint "theme_id", default: 1
    t.boolean "use_elasticsearch", default: false
    t.integer "expire_password_after_in_days", default: 90, null: false
    t.text "auth_domain", default: [], array: true
    t.text "allowed_sites", default: [], array: true
    t.boolean "enable_google_oauth", default: false, null: false
    t.boolean "enable_email_and_password_login", default: true, null: false
    t.boolean "enable_microsoft_oauth", default: false, null: false
    t.boolean "enable_internationalization", default: false, null: false
    t.boolean "enable_signup", default: false, null: false
    t.uuid "default_department_id"
    t.string "welcome_video_url"
    t.boolean "chat_enabled", default: false
    t.string "twilio_api_key"
    t.string "twilio_api_secret"
    t.string "twilio_auth_token"
    t.string "twilio_chat_service_sid"
    t.string "twilio_sid"
    t.boolean "restrict_access_by_ip", default: false, null: false
    t.string "allowed_ips", default: [], null: false, array: true
    t.integer "token_life_span_in_minutes", default: 4320, null: false
    t.string "waf_ip_set_id"
    t.string "waf_rule_group_id"
    t.boolean "limit_user_on_signup", default: false, null: false
    t.jsonb "open_id_config"
    t.boolean "enable_open_id", default: false
    t.boolean "bypass_approval", default: false, null: false
    t.string "block_menus", default: [], array: true
    t.boolean "chatbot_enabled", default: false
    t.boolean "contact_us_enabled", default: false
    t.string "page_title"
    t.boolean "disable_tips", default: false
    t.boolean "smtp_custom", default: false
    t.string "smtp_address"
    t.string "smtp_port"
    t.string "smtp_username"
    t.string "smtp_password"
    t.string "smtp_from"
    t.boolean "smtp_starttls", default: true
    t.boolean "not_validate_auth_domain_openid", default: false, null: false
    t.string "custom_openid_name"
    t.index ["theme_id"], name: "index_companies_on_theme_id"
  end

  create_table "contacts", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "description"
    t.string "email"
    t.string "phone", limit: 11
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "ddi", limit: 4
    t.string "ddd", limit: 4
  end

  create_table "content_destroyings", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "bulk_destroying_content_id"
    t.string "processing_errors", default: [], array: true
    t.integer "status", default: 0, null: false
    t.text "content_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["bulk_destroying_content_id"], name: "index_content_destroyings_on_bulk_destroying_content_id"
  end

  create_table "content_values", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "field_id"
    t.uuid "answer_id"
    t.uuid "content_id"
    t.string "value"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["answer_id"], name: "index_content_values_on_answer_id"
    t.index ["content_id"], name: "index_content_values_on_content_id"
    t.index ["field_id"], name: "index_content_values_on_field_id"
  end

  create_table "contents", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "business_id"
    t.datetime "deleted_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "name", default: "", null: false
    t.boolean "draft", default: false
    t.integer "status", default: 0, null: false
    t.datetime "concluded_at", precision: nil
    t.uuid "created_by_id"
    t.uuid "parent_id"
    t.uuid "current_answer_id"
    t.text "note"
    t.string "keywords"
    t.inet "created_by_ip"
    t.string "deletion_reason"
    t.uuid "deleted_by_id"
    t.index ["business_id"], name: "index_contents_on_business_id"
    t.index ["created_by_id"], name: "index_contents_on_created_by_id"
    t.index ["current_answer_id"], name: "index_contents_on_current_answer_id"
    t.index ["deleted_at"], name: "index_contents_on_deleted_at"
    t.index ["deleted_by_id"], name: "index_contents_on_deleted_by_id"
    t.index ["draft"], name: "index_contents_on_draft"
    t.index ["keywords", "id", "status", "note", "current_answer_id", "deleted_at"], name: "index_contents_on_list_query"
    t.index ["parent_id"], name: "index_contents_on_parent_id"
    t.index ["status"], name: "index_contents_on_status"
  end

  create_table "data_replacements", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "text", null: false
    t.string "replacement"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "departments", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "name", null: false
    t.datetime "deleted_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "limited", default: false
  end

  create_table "departments_statistics", id: false, force: :cascade do |t|
    t.uuid "department_id", null: false
    t.uuid "statistic_id", null: false
  end

  create_table "departments_users", id: false, force: :cascade do |t|
    t.uuid "department_id"
    t.uuid "user_id"
  end

  create_table "dependent_field_rules", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "parent_field_id"
    t.uuid "parent_step_id"
    t.uuid "field_id"
    t.uuid "step_id"
    t.integer "condition_operator"
    t.string "condition_value"
    t.boolean "field_required"
    t.string "field_value"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "field_operator"
    t.string "error_message"
    t.boolean "field_disabled", default: false
    t.boolean "field_hidden", default: false
    t.boolean "field_visible", default: false
    t.jsonb "rules"
    t.jsonb "rule_actions"
    t.uuid "business_id"
    t.string "description", limit: 50, default: ""
    t.datetime "deleted_at"
    t.jsonb "restrictions", default: []
    t.integer "rule_type", default: 0, null: false
    t.index ["business_id"], name: "index_dependent_field_rules_on_business_id"
    t.index ["field_id"], name: "index_dependent_field_rules_on_field_id"
    t.index ["parent_field_id"], name: "index_dependent_field_rules_on_parent_field_id"
    t.index ["parent_step_id"], name: "index_dependent_field_rules_on_parent_step_id"
    t.index ["restrictions"], name: "index_dependent_field_rules_on_restrictions", using: :gin
    t.index ["step_id"], name: "index_dependent_field_rules_on_step_id"
  end

  create_table "dependent_reference_fields", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "step_id"
    t.uuid "field_id"
    t.uuid "parent_field_id"
    t.uuid "parent_step_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["field_id"], name: "index_dependent_reference_fields_on_field_id"
    t.index ["parent_field_id"], name: "index_dependent_reference_fields_on_parent_field_id"
    t.index ["parent_step_id"], name: "index_dependent_reference_fields_on_parent_step_id"
    t.index ["step_id"], name: "index_dependent_reference_fields_on_step_id"
  end

  create_table "favorites", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "user_id"
    t.uuid "business_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["business_id"], name: "index_favorites_on_business_id"
    t.index ["user_id"], name: "index_favorites_on_user_id"
  end

  create_table "field_options", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "field_id"
    t.uuid "content_id"
    t.string "value", null: false
    t.integer "order", null: false
    t.string "label", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["content_id"], name: "index_field_options_on_content_id"
    t.index ["field_id"], name: "index_field_options_on_field_id"
    t.index ["label"], name: "index_field_options_on_label"
    t.index ["value"], name: "index_field_options_on_value"
  end

  create_table "field_validations", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "field_id"
    t.integer "type", null: false
    t.integer "operator", null: false
    t.string "data"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "error_message", default: "", null: false
    t.index ["field_id"], name: "index_field_validations_on_field_id"
  end

  create_table "fields", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.integer "type", null: false
    t.integer "size", null: false
    t.string "label", null: false
    t.string "tooltip"
    t.datetime "deleted_at", precision: nil
    t.integer "order", null: false
    t.uuid "template_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "required", default: false
    t.json "options"
    t.uuid "reference_business_id"
    t.uuid "reference_field_id"
    t.uuid "reference_value_field_id"
    t.boolean "visible", default: true
    t.boolean "enabled", default: true
    t.boolean "show_on_form", default: false
    t.uuid "reference_sub_business_id"
    t.string "input_variable"
    t.string "output_variable"
    t.boolean "allow_add_new", default: false
    t.boolean "update_disabled", default: false
    t.integer "text_transformation"
    t.integer "height", default: 0
    t.boolean "reference_value_use_key_fields", default: false
    t.boolean "enable_char_count", default: false, null: false
    t.integer "char_max_limit"
    t.boolean "allow_to_select_deleted_option", default: false
    t.boolean "validate_sub_business_contents"
    t.jsonb "default_value"
    t.index ["deleted_at"], name: "index_fields_on_deleted_at"
    t.index ["reference_business_id"], name: "index_fields_on_reference_business_id"
    t.index ["reference_field_id"], name: "index_fields_on_reference_field_id"
    t.index ["reference_sub_business_id"], name: "index_fields_on_reference_sub_business_id"
    t.index ["reference_value_field_id"], name: "index_fields_on_reference_value_field_id"
    t.index ["template_id"], name: "index_fields_on_template_id"
  end

  create_table "fields_searches", id: false, force: :cascade do |t|
    t.uuid "search_id", null: false
    t.uuid "field_id", null: false
  end

  create_table "notifications", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "title", null: false
    t.text "message", null: false
    t.datetime "discarded_at", precision: nil
    t.uuid "user_id", null: false
    t.uuid "dismissed_by", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.uuid "destiny_users", default: [], array: true
    t.uuid "destiny_departments", default: [], array: true
    t.index ["discarded_at"], name: "index_notifications_on_discarded_at"
    t.index ["user_id"], name: "index_notifications_on_user_id"
  end

  create_table "old_passwords", force: :cascade do |t|
    t.string "encrypted_password", null: false
    t.string "password_archivable_type", null: false
    t.string "password_archivable_id", null: false
    t.string "password_salt"
    t.datetime "created_at", precision: nil
    t.index ["password_archivable_type", "password_archivable_id"], name: "index_password_archivable"
  end

  create_table "searches", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.uuid "user_id", null: false
    t.uuid "business_id", null: false
    t.text "query"
    t.text "field_names", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "kind", default: 0
    t.index ["business_id"], name: "index_searches_on_business_id"
    t.index ["user_id"], name: "index_searches_on_user_id"
  end

  create_table "show_on_list_fields", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "business_id"
    t.uuid "field_id"
    t.integer "order", null: false
    t.integer "order_contents", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.uuid "step_id"
    t.index ["business_id"], name: "index_show_on_list_fields_on_business_id"
    t.index ["field_id"], name: "index_show_on_list_fields_on_field_id"
    t.index ["step_id"], name: "index_show_on_list_fields_on_step_id", where: "(step_id IS NOT NULL)"
  end

  create_table "statistics", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "step_permissions", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "step_id", null: false
    t.uuid "user_id"
    t.uuid "department_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "scope", default: 0, null: false
    t.index ["department_id"], name: "index_step_permissions_on_department_id"
    t.index ["step_id"], name: "index_step_permissions_on_step_id"
    t.index ["user_id"], name: "index_step_permissions_on_user_id"
  end

  create_table "step_templates", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "step_id"
    t.uuid "template_id"
    t.integer "order", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["step_id"], name: "index_step_templates_on_step_id"
    t.index ["template_id"], name: "index_step_templates_on_template_id"
  end

  create_table "steps", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.uuid "business_id"
    t.datetime "deleted_at", precision: nil
    t.integer "order", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "validation_url"
    t.string "verification_url"
    t.string "success_message", default: "Dados salvos com sucesso!"
    t.uuid "step_for_revision_id"
    t.boolean "send_email_to_user_who_registered", default: false, null: false
    t.boolean "send_email_to_all_with_access", default: false, null: false
    t.text "steps_to_change_ids", default: [], array: true
    t.boolean "require_credentials", default: false, null: false
    t.boolean "require_credentials_use_any_email", default: false, null: false
    t.uuid "step_for_revision_ids", default: [], array: true
    t.boolean "not_editable", default: false
    t.index ["business_id"], name: "index_steps_on_business_id"
    t.index ["deleted_at"], name: "index_steps_on_deleted_at"
    t.index ["step_for_revision_id"], name: "index_steps_on_step_for_revision_id"
  end

  create_table "templates", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "name", null: false
    t.text "description"
    t.datetime "deleted_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "variable"
    t.index ["deleted_at"], name: "index_templates_on_deleted_at"
    t.index ["name"], name: "index_templates_on_name"
  end

  create_table "themes", force: :cascade do |t|
    t.string "name", null: false
    t.string "text_color", null: false
    t.string "background_color", null: false
    t.string "button_color", null: false
    t.string "menu_color", null: false
    t.string "menu_focus_color", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "translations", force: :cascade do |t|
    t.string "actable_id", null: false
    t.string "actable_type", null: false
    t.integer "language", null: false
    t.string "translated_text", null: false
    t.string "attribute_name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "troubleshootings", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.datetime "start_at", precision: nil
    t.datetime "end_at", precision: nil
    t.string "url"
    t.jsonb "data"
    t.boolean "external"
    t.integer "response_code"
    t.float "duration_in_seconds"
    t.string "subdomain"
    t.string "thread_id"
    t.string "request_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "users", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "provider", default: "email", null: false
    t.string "uid", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "unconfirmed_email"
    t.string "name"
    t.string "nickname"
    t.string "image"
    t.string "email"
    t.jsonb "tokens"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.boolean "limited", default: false
    t.boolean "coordinator", default: false
    t.datetime "password_changed_at", precision: nil
    t.boolean "allow_password_change", default: false, null: false
    t.boolean "approved", default: true, null: false
    t.boolean "notification", default: true, null: false
    t.integer "failed_attempts", default: 0, null: false
    t.datetime "locked_at", precision: nil
    t.string "unlock_token"
    t.boolean "welcome_video_watched", default: false
    t.string "block_menus", default: [], array: true
    t.boolean "chat_enabled", default: false
    t.datetime "last_active_at", precision: nil
    t.string "current_departments", default: [], null: false, array: true
    t.string "ddi", limit: 4, default: ""
    t.string "ddd", limit: 4, default: ""
    t.string "phone", limit: 11, default: ""
    t.string "authorization_token"
    t.index ["deleted_at"], name: "index_users_on_deleted_at"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["password_changed_at"], name: "index_users_on_password_changed_at"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["uid", "provider"], name: "index_users_on_uid_and_provider", unique: true
    t.index ["unlock_token"], name: "index_users_on_unlock_token", unique: true
  end

  create_table "versions", force: :cascade do |t|
    t.string "item_type", null: false
    t.uuid "item_id", null: false
    t.string "event", null: false
    t.uuid "whodunnit"
    t.string "whodunnit_type"
    t.jsonb "object"
    t.jsonb "object_changes"
    t.datetime "created_at", precision: nil
    t.inet "ip"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  create_table "widgets", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "statistic_id", null: false
    t.uuid "search_id", null: false
    t.integer "size", default: 0, null: false
    t.integer "chart_type", default: 0, null: false
    t.string "embedded_code"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.uuid "category_field_id"
    t.uuid "value_field_id"
    t.integer "order", default: 0, null: false
    t.string "category_agg"
    t.string "value_agg"
    t.index ["category_field_id"], name: "index_widgets_on_category_field_id"
    t.index ["search_id"], name: "index_widgets_on_search_id"
    t.index ["statistic_id"], name: "index_widgets_on_statistic_id"
    t.index ["value_field_id"], name: "index_widgets_on_value_field_id"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "alteration_processings", "bulk_saving_answers"
  add_foreign_key "answer_processings", "answers"
  add_foreign_key "answer_processings", "bulk_saving_answers"
  add_foreign_key "answer_versions", "answers"
  add_foreign_key "answer_versions", "users", column: "whodunnit_id"
  add_foreign_key "answers", "contents"
  add_foreign_key "answers", "steps"
  add_foreign_key "answers", "users"
  add_foreign_key "answers", "users", column: "authorizer_id"
  add_foreign_key "answers", "users", column: "created_by_id"
  add_foreign_key "answers", "users", column: "review_requested_by_id"
  add_foreign_key "bulk_destroying_contents", "companies"
  add_foreign_key "bulk_saving_answers", "businesses"
  add_foreign_key "bulk_saving_answers", "steps"
  add_foreign_key "bulk_saving_answers", "users"
  add_foreign_key "business_headers", "businesses"
  add_foreign_key "business_headers", "fields"
  add_foreign_key "business_headers", "steps"
  add_foreign_key "businesses", "business_groups"
  add_foreign_key "businesses_fields", "businesses"
  add_foreign_key "businesses_fields", "fields"
  add_foreign_key "content_destroyings", "bulk_destroying_contents"
  add_foreign_key "content_values", "answers"
  add_foreign_key "content_values", "contents"
  add_foreign_key "content_values", "fields"
  add_foreign_key "contents", "answers", column: "current_answer_id", deferrable: :deferred
  add_foreign_key "contents", "businesses"
  add_foreign_key "contents", "contents", column: "parent_id", deferrable: :deferred
  add_foreign_key "contents", "users", column: "created_by_id"
  add_foreign_key "contents", "users", column: "deleted_by_id"
  add_foreign_key "dependent_field_rules", "businesses"
  add_foreign_key "dependent_field_rules", "fields"
  add_foreign_key "dependent_field_rules", "fields", column: "parent_field_id"
  add_foreign_key "dependent_field_rules", "steps"
  add_foreign_key "dependent_field_rules", "steps", column: "parent_step_id"
  add_foreign_key "dependent_reference_fields", "fields"
  add_foreign_key "dependent_reference_fields", "fields", column: "parent_field_id"
  add_foreign_key "dependent_reference_fields", "steps"
  add_foreign_key "dependent_reference_fields", "steps", column: "parent_step_id"
  add_foreign_key "favorites", "businesses"
  add_foreign_key "favorites", "users"
  add_foreign_key "field_options", "contents"
  add_foreign_key "field_options", "fields"
  add_foreign_key "field_validations", "fields"
  add_foreign_key "fields", "businesses", column: "reference_business_id"
  add_foreign_key "fields", "businesses", column: "reference_sub_business_id"
  add_foreign_key "fields", "fields", column: "reference_field_id"
  add_foreign_key "fields", "fields", column: "reference_value_field_id"
  add_foreign_key "fields", "templates"
  add_foreign_key "notifications", "users"
  add_foreign_key "searches", "businesses"
  add_foreign_key "searches", "users"
  add_foreign_key "show_on_list_fields", "businesses"
  add_foreign_key "show_on_list_fields", "fields"
  add_foreign_key "show_on_list_fields", "steps"
  add_foreign_key "step_permissions", "departments"
  add_foreign_key "step_permissions", "steps"
  add_foreign_key "step_permissions", "users"
  add_foreign_key "step_templates", "steps"
  add_foreign_key "step_templates", "templates"
  add_foreign_key "steps", "businesses"
  add_foreign_key "steps", "steps", column: "step_for_revision_id", deferrable: :deferred
  add_foreign_key "widgets", "fields", column: "category_field_id"
  add_foreign_key "widgets", "fields", column: "value_field_id"
  add_foreign_key "widgets", "searches"
  add_foreign_key "widgets", "statistics"
end
