# frozen_string_literal: true

module Elasticsearch
  module Constants
    # Pagination and sizing
    PAGE_SIZE = 100
    OCCURRENCE_RESULTS = 16
    PATTERN_RESULTS = 10
    SIMILAR_RESULTS = 16
    MAX_TEXT_SIZE = 30_000
    MAPPING_TOTAL_FIELDS_LIMIT = 3000

    # Document field names
    CONTENT_ID = '__CONTENT_ID'
    BUSINESS_ID = '__BUSINESS_ID'
    NOTE = '__NOTA'
    CONTENT_STATUS = '__CONTENT_STATUS'
    BUSINESS_NAME = '__NOME_NEGOCIO'
    DELETION_REASON = '__MOTIVO_DELECAO'
    PARENT_ID = '__PARENT_ID'
    DELETED_AT = '__INATIVO_EM'
    CREATED_AT = '__CRIADO_EM'
    UPDATED_AT = '__ATUALIZADO_EM'
    UPDATED_BY_NAME = '__ATUALIZADO_POR'

    # Statistics types
    MISSING_STAT = 'missing'
    OCCURRENCE_STAT = 'occurrence'
    OCCURRENCE_COMPOSITE_STAT = 'occurrence_composite'
    PATTERN_STAT = 'pattern'
    PRESENCE_STAT = 'present'
    SIMILARITY_STAT = 'similarity'
    STRING_STAT = 'stats'
    UNIQUENESS_STAT = 'uniqueness'

    # Search configuration
    PAGINATION_KEY = 'after_key'
    SEARCH_FUZZINESS = 'AUTO:3,6'
    SIMILAR_TERMS_FUZZINESS = 'AUTO:2,4'

    # Elasticsearch meta keys for aggregations
    META_KEYS = %w[
      aggs avg bool bucket_script bucket_selector cardinality children composite
      date_histogram date_range derivative extended_stats filter filters geo_bounds
      geo_centroid geo_distance geo_hash_grid geohash_grid global histogram ip_range
      max median_absolute_deviation min missing moving_avg nested parent percentiles
      percentile_ranks range rate reverse_nested sampler scripted_metric serial_diff
      significant_terms stats string_stats sum terms top_hits t-test value_count
      weighted_avg percentile unique_count geo_bounds geo_centroid max_bucket min_bucket
      moving_fn moving_percentiles percentile_ranks scripted_metric stats string_stats
      top_metrics top_hits matrix_stats scripted_sparse_vector field size script lang
      source format
    ].freeze

    # Field types that support different elasticsearch mappings
    NESTED_FIELD_TYPES = %w[nested object].freeze
    SUB_BUSINESS_FIELD_TYPE = 'sub_business'

    # Valid field types for value processing
    NUMERIC_FIELD_TYPES = %w[integer decimal].freeze
    DATE_FIELD_TYPE = 'date'
    LINK_FIELD_TYPE = 'link'
    UPLOAD_FIELD_TYPE = 'upload'

    # Regular expressions for field validation
    INTEGER_REGEX = /^\d+$/
    DECIMAL_REGEX = /^\d+(?:\.\d+)?$/
    DATE_REGEX = /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$/

    # Index naming methods
    module_function

    def index_name(business_id)
      "#{Apartment::Tenant.current}-#{business_id}"
    end

    def wildcard_index_name
      "#{Apartment::Tenant.current}-*"
    end
  end
end
