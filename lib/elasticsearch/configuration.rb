# frozen_string_literal: true

module Elasticsearch
  module Configuration
    class << self
      def client
        @client = ::Elasticsearch::Client.new(client_options)
      end

      def reset_client!
        @client = nil
      end

      private

      def client_options
        options = {
          url: elasticsearch_url,
          log: log_enabled?
        }

        # Use API key if available, otherwise fall back to basic auth
        if elasticsearch_api_key.present?
          options[:api_key] = elasticsearch_api_key
        elsif elasticsearch_user.present? && elasticsearch_password.present?
          options[:user] = elasticsearch_user
          options[:password] = elasticsearch_password
        end

        options
      end

      private

      def elasticsearch_url
        Rails.application.credentials[:elasticsearch_url]
      end

      def elasticsearch_api_key
        Rails.application.credentials[:elasticsearch_api_key]
      end

      def elasticsearch_user
        Rails.application.credentials[:elasticsearch_user]
      end

      def elasticsearch_password
        Rails.application.credentials[:elasticsearch_password]
      end

      def log_enabled?
        Rails.env.development? || Rails.env.test?
      end
    end
  end
end
